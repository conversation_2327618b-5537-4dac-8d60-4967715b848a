
name: "attention_rescoring"
platform: "ensemble"
max_batch_size: 64 #MAX_BATCH

input [
  {
    name: "WAV"
    data_type: TYPE_FP32
    dims: [-1]
  },
  {
    name: "WAV_LENS"
    data_type: TYPE_INT32
    dims: [1]
  }
]

output [
  {
    name: "TRANSCRIPTS"
    data_type: TYPE_STRING
    dims: [1]
  }
]

ensemble_scheduling {
 step [
   {
    model_name: "feature_extractor"
    model_version: -1
    input_map {
      key: "wav"
      value: "WAV"
    }
    input_map {
      key: "wav_lens"
      value: "WAV_LENS"
    }
    output_map {
      key: "speech"
      value: "SPEECH"
    }
    output_map {
      key: "speech_lengths"
      value: "SPEECH_LENGTHS"
    }
   },
   {
    model_name: "encoder"
    model_version: -1
    input_map {
      key: "speech"
      value: "SPEECH"
    }
    input_map {
      key: "speech_lengths"
      value: "SPEECH_LENGTHS"
    }
    output_map {
      key: "encoder_out"
      value: "encoder_out"
    }
    output_map {
      key: "encoder_out_lens"
      value: "encoder_out_lens"
    }
    output_map {
        key: "beam_log_probs"
        value: "beam_log_probs"
    }
    output_map {
        key: "beam_log_probs_idx"
        value: "beam_log_probs_idx"
    }
  },
  {
      model_name: "scoring"
      model_version: -1
      input_map {
          key: "encoder_out"
          value: "encoder_out"
      }
      input_map {
          key: "encoder_out_lens"
          value: "encoder_out_lens"
      }
      input_map {
          key: "batch_log_probs"
          value: "beam_log_probs"
      }
      input_map {
          key: "batch_log_probs_idx"
          value: "beam_log_probs_idx"
      }
      output_map {
          key: "OUTPUT0"
          value: "TRANSCRIPTS"
      }
  }
  ]
}

