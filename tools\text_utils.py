from itn.chinese.inverse_normalizer import InverseNormalizer as zh_ITNer

class MultiLangITN:
    def __init__(self, langs = ['zh', 'en'], cache_dirs = None):
        self.normalizers = {}
        if cache_dirs is not None:
            assert isinstance(cache_dirs, list) and len(cache_dirs) == len(langs)
        else:
            cache_dirs = [f'res/post/itn/{l}' for l in langs]
        for lang, cache_dir in zip(langs, cache_dirs):
            if lang == 'zh':
                normalizer = zh_ITNer(
                        cache_dir=cache_dir, 
                        overwrite_cache=False,
                        enable_standalone_number=True,
                        enable_0_to_9=True)
                self.normalizers[lang] = normalizer
            elif lang == 'en':
                pass 
        
    def normalize(self, lang, text):
        assert lang in self.normalizers
        #print(self.normalizers[lang].tag(args.text))
        return self.normalizers[lang].normalize(text)

def main(lang, text = None, text_file = None):
    assert text is None or text_file is None
    itn_engine = MultiLangITN([lang])

    texts = []
    if text is not None:
        texts.append(text)
    if text_file is not None:
        with open(text_file, "r") as fin:
            for line in fin:
                if len(line.strip()) > 0:
                    texts.append(line.strip())

    for text in texts:
        itn_text = itn_engine.normalize(lang, text)
        print(f'{text} ==> {itn_text}', flush = True)

if __name__ == "__main__":
    import fire
    fire.Fire(main)
