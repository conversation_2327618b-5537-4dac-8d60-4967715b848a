I0611 10:24:57.826472 136426 pinned_memory_manager.cc:240] Pinned memory pool is created at '0x7f7e60000000' with size 1024000000
I0611 10:24:57.827219 136426 cuda_memory_manager.cc:105] CUDA memory pool is created on device 0 with size 1024000000
E0611 10:24:57.835029 136426 model_repository_manager.cc:1004] Poll failed for model directory 'attention_rescoring': unexpected directory name 'attention_rescoring' for model 'attention_rescoring_zh', directory name must equal model name
E0611 10:24:57.835449 136426 model_repository_manager.cc:1004] Poll failed for model directory 'decoder': unexpected directory name 'decoder' for model 'decoder_zh', directory name must equal model name
E0611 10:24:57.835572 136426 model_repository_manager.cc:1004] Poll failed for model directory 'encoder': unexpected directory name 'encoder' for model 'encoder_zh', directory name must equal model name
E0611 10:24:57.835776 136426 model_repository_manager.cc:1004] Poll failed for model directory 'punc': unexpected directory name 'punc' for model 'punc_zh', directory name must equal model name
E0611 10:24:57.835939 136426 model_repository_manager.cc:1004] Poll failed for model directory 'scoring': unexpected directory name 'scoring' for model 'scoring_zh', directory name must equal model name
I0611 10:24:57.836027 136426 model_lifecycle.cc:459] loading: audio_file_trans:1
I0611 10:24:57.836080 136426 model_lifecycle.cc:459] loading: audio_trans:1
I0611 10:24:57.836143 136426 model_lifecycle.cc:459] loading: feature_extractor:1
********/ws/log/zh****************
free(): invalid pointer
free(): invalid pointer
free(): invalid pointer
I0611 10:25:08.218730 136426 python_be.cc:1858] TRITONBACKEND_ModelInstanceInitialize: audio_file_trans_0 (CPU device 0)
I0611 10:25:10.212939 136426 pb_stub.cc:314] Failed to initialize Python stub: FileNotFoundError: [Errno 2] No such file or directory: '/ws/log_zh/log.audio_file_trans.136426_139860953927680.2025-06-11_10-25-10.txt'

At:
  /usr/lib/python3.8/logging/__init__.py(1176): _open
  /usr/lib/python3.8/logging/__init__.py(1147): __init__
  /ws/tools/model_repo_utils.py(16): create_logger
  /ws/model/zh/audio_file_trans/1/model.py(59): initialize

free(): invalid pointer
I0611 10:25:10.868420 136426 python_be.cc:1858] TRITONBACKEND_ModelInstanceInitialize: audio_trans_0 (CPU device 0)
E0611 10:25:10.887054 136426 model_lifecycle.cc:597] failed to load 'audio_file_trans' version 1: Internal: FileNotFoundError: [Errno 2] No such file or directory: '/ws/log_zh/log.audio_file_trans.136426_139860953927680.2025-06-11_10-25-10.txt'

At:
  /usr/lib/python3.8/logging/__init__.py(1176): _open
  /usr/lib/python3.8/logging/__init__.py(1147): __init__
  /ws/tools/model_repo_utils.py(16): create_logger
  /ws/model/zh/audio_file_trans/1/model.py(59): initialize

I0611 10:25:12.839369 136426 pb_stub.cc:314] Failed to initialize Python stub: FileNotFoundError: [Errno 2] No such file or directory: '/ws/log_zh/log.audio_trans.136426_140646604787712.2025-06-11_10-25-12.txt'

At:
  /usr/lib/python3.8/logging/__init__.py(1176): _open
  /usr/lib/python3.8/logging/__init__.py(1147): __init__
  /ws/tools/model_repo_utils.py(16): create_logger
  /ws/model/zh/audio_trans/1/model.py(59): initialize

free(): invalid pointer
I0611 10:25:13.508223 136426 python_be.cc:1858] TRITONBACKEND_ModelInstanceInitialize: feature_extractor_0 (GPU device 0)
E0611 10:25:13.526714 136426 model_lifecycle.cc:597] failed to load 'audio_trans' version 1: Internal: FileNotFoundError: [Errno 2] No such file or directory: '/ws/log_zh/log.audio_trans.136426_140646604787712.2025-06-11_10-25-12.txt'

At:
  /usr/lib/python3.8/logging/__init__.py(1176): _open
  /usr/lib/python3.8/logging/__init__.py(1147): __init__
  /ws/tools/model_repo_utils.py(16): create_logger
  /ws/model/zh/audio_trans/1/model.py(59): initialize

I0611 10:25:15.374088 136426 model_lifecycle.cc:694] successfully loaded 'feature_extractor' version 1
I0611 10:25:15.374429 136426 server.cc:563] 
+------------------+------+
| Repository Agent | Path |
+------------------+------+
+------------------+------+

I0611 10:25:15.374765 136426 server.cc:590] 
+---------+-------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Backend | Path                                                  | Config                                                                                                                                                        |
+---------+-------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------+
| python  | /opt/tritonserver/backends/python/libtriton_python.so | {"cmdline":{"auto-complete-config":"true","min-compute-capability":"6.000000","backend-directory":"/opt/tritonserver/backends","default-max-batch-size":"4"}} |
+---------+-------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------+

I0611 10:25:15.375155 136426 server.cc:633] 
+-------------------+---------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Model             | Version | Status                                                                                                                                                          |
+-------------------+---------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------+
| audio_file_trans  | 1       | UNAVAILABLE: Internal: FileNotFoundError: [Errno 2] No such file or directory: '/ws/log_zh/log.audio_file_trans.136426_139860953927680.2025-06-11_10-25-10.txt' |
|                   |         |                                                                                                                                                                 |
|                   |         | At:                                                                                                                                                             |
|                   |         |   /usr/lib/python3.8/logging/__init__.py(1176): _open                                                                                                           |
|                   |         |   /usr/lib/python3.8/logging/__init__.py(1147): __init__                                                                                                        |
|                   |         |   /ws/tools/model_repo_utils.py(16): create_logger                                                                                                              |
|                   |         |   /ws/model/zh/audio_file_trans/1/model.py(59): initialize                                                                                                      |
| audio_trans       | 1       | UNAVAILABLE: Internal: FileNotFoundError: [Errno 2] No such file or directory: '/ws/log_zh/log.audio_trans.136426_140646604787712.2025-06-11_10-25-12.txt'      |
|                   |         |                                                                                                                                                                 |
|                   |         | At:                                                                                                                                                             |
|                   |         |   /usr/lib/python3.8/logging/__init__.py(1176): _open                                                                                                           |
|                   |         |   /usr/lib/python3.8/logging/__init__.py(1147): __init__                                                                                                        |
|                   |         |   /ws/tools/model_repo_utils.py(16): create_logger                                                                                                              |
|                   |         |   /ws/model/zh/audio_trans/1/model.py(59): initialize                                                                                                           |
| feature_extractor | 1       | READY                                                                                                                                                           |
+-------------------+---------+-----------------------------------------------------------------------------------------------------------------------------------------------------------------+

I0611 10:25:15.505257 136426 metrics.cc:864] Collecting metrics for GPU 0: NVIDIA RTX A6000
I0611 10:25:15.505572 136426 metrics.cc:757] Collecting CPU metrics
I0611 10:25:15.505768 136426 tritonserver.cc:2264] 
+----------------------------------+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Option                           | Value                                                                                                                                                                                                |
+----------------------------------+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| server_id                        | triton                                                                                                                                                                                               |
| server_version                   | 2.30.0                                                                                                                                                                                               |
| server_extensions                | classification sequence model_repository model_repository(unload_dependents) schedule_policy model_configuration system_shared_memory cuda_shared_memory binary_tensor_data statistics trace logging |
| model_repository_path[0]         | /ws/model/zh                                                                                                                                                                                         |
| model_control_mode               | MODE_NONE                                                                                                                                                                                            |
| strict_model_config              | 0                                                                                                                                                                                                    |
| rate_limit                       | OFF                                                                                                                                                                                                  |
| pinned_memory_pool_byte_size     | 1024000000                                                                                                                                                                                           |
| cuda_memory_pool_byte_size{0}    | 1024000000                                                                                                                                                                                           |
| response_cache_byte_size         | 0                                                                                                                                                                                                    |
| min_supported_compute_capability | 6.0                                                                                                                                                                                                  |
| strict_readiness                 | 1                                                                                                                                                                                                    |
| exit_timeout                     | 30                                                                                                                                                                                                   |
+----------------------------------+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+

I0611 10:25:15.505809 136426 server.cc:264] Waiting for in-flight requests to complete.
I0611 10:25:15.505832 136426 server.cc:280] Timeout 30: Found 0 model versions that have in-flight inferences
I0611 10:25:15.505918 136426 server.cc:295] All models are stopped, unloading models
I0611 10:25:15.505947 136426 server.cc:302] Timeout 30: Found 1 live models and 0 in-flight non-inference requests
I0611 10:25:16.506058 136426 server.cc:302] Timeout 29: Found 1 live models and 0 in-flight non-inference requests
free(): invalid pointer
I0611 10:25:17.080336 136426 model_lifecycle.cc:579] successfully unloaded 'feature_extractor' version 1
I0611 10:25:17.506259 136426 server.cc:302] Timeout 28: Found 0 live models and 0 in-flight non-inference requests
error: creating server: Internal - failed to load all models
