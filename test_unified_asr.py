#!/usr/bin/env python3
"""
Test script for the unified ASR model
"""

import sys
import os
import numpy as np
import json

# Add tools to path
sys.path.append('tools')

def test_config_parsing():
    """Test configuration file parsing"""
    config_path = "model/zh/unified_asr/config.pbtxt"
    
    print("Testing configuration file...")
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            content = f.read()
        print(f"✓ Configuration file exists and readable")
        print(f"  File size: {len(content)} bytes")
        
        # Check for required parameters
        required_params = [
            'vad_enable', 'sample_rate', 'num_mel_bins', 
            'encoder_model_path', 'vocabulary', 'language'
        ]
        
        for param in required_params:
            if param in content:
                print(f"  ✓ Found parameter: {param}")
            else:
                print(f"  ✗ Missing parameter: {param}")
    else:
        print(f"✗ Configuration file not found: {config_path}")

def test_model_imports():
    """Test if the model can import required modules"""
    print("\nTesting model imports...")
    
    try:
        # Test basic imports
        import torch
        print("  ✓ PyTorch available")
        
        import kaldifeat
        print("  ✓ Kaldifeat available")
        
        import onnxruntime as ort
        print("  ✓ ONNX Runtime available")
        
        # Test CTC decoder
        try:
            from swig_decoders import ctc_beam_search_decoder_batch, PathTrie, map_batch
            print("  ✓ CTC decoder available")
        except ImportError as e:
            print(f"  ✗ CTC decoder not available: {e}")
        
        # Test tools
        try:
            from audio_utils import load_from_local_path
            print("  ✓ Audio utils available")
        except ImportError as e:
            print(f"  ✗ Audio utils not available: {e}")
            
        try:
            from vad_utils import SpeechVadFrontend
            print("  ✓ VAD utils available")
        except ImportError as e:
            print(f"  ✗ VAD utils not available: {e}")
            
        try:
            from model_repo_utils import create_logger
            print("  ✓ Model repo utils available")
        except ImportError as e:
            print(f"  ✗ Model repo utils not available: {e}")
            
    except ImportError as e:
        print(f"  ✗ Import error: {e}")

def test_model_structure():
    """Test the model file structure"""
    print("\nTesting model structure...")
    
    model_path = "model/zh/unified_asr/1/model.py"
    if os.path.exists(model_path):
        print(f"  ✓ Model file exists: {model_path}")
        
        with open(model_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required methods
        required_methods = [
            'class TritonPythonModel',
            'def initialize',
            'def execute',
            'def finalize',
            'def _extract_features',
            'def _run_encoder',
            'def _decode_ctc'
        ]
        
        for method in required_methods:
            if method in content:
                print(f"    ✓ Found: {method}")
            else:
                print(f"    ✗ Missing: {method}")
                
    else:
        print(f"  ✗ Model file not found: {model_path}")

def test_directory_structure():
    """Test the directory structure"""
    print("\nTesting directory structure...")
    
    required_dirs = [
        "model/zh/unified_asr",
        "model/zh/unified_asr/1",
        "tools"
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"  ✓ Directory exists: {dir_path}")
        else:
            print(f"  ✗ Directory missing: {dir_path}")

def compare_with_original():
    """Compare with original models"""
    print("\nComparing with original models...")
    
    original_models = [
        "model/zh/audio_trans",
        "model/zh/audio_file_trans",
        "model/zh/feature_extractor",
        "model/zh/encoder",
        "model/zh/scoring"
    ]
    
    for model_path in original_models:
        if os.path.exists(model_path):
            print(f"  ✓ Original model exists: {model_path}")
        else:
            print(f"  ✗ Original model missing: {model_path}")

def main():
    """Run all tests"""
    print("=" * 60)
    print("UNIFIED ASR MODEL TEST SUITE")
    print("=" * 60)
    
    test_config_parsing()
    test_model_imports()
    test_model_structure()
    test_directory_structure()
    compare_with_original()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
