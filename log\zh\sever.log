I0611 10:27:04.816489 140223 pinned_memory_manager.cc:240] Pinned memory pool is created at '0x7f28c8000000' with size 1024000000
I0611 10:27:04.817249 140223 cuda_memory_manager.cc:105] CUDA memory pool is created on device 0 with size 1024000000
E0611 10:27:04.825041 140223 model_repository_manager.cc:1004] Poll failed for model directory 'attention_rescoring': unexpected directory name 'attention_rescoring' for model 'attention_rescoring_zh', directory name must equal model name
E0611 10:27:04.825476 140223 model_repository_manager.cc:1004] Poll failed for model directory 'decoder': unexpected directory name 'decoder' for model 'decoder_zh', directory name must equal model name
E0611 10:27:04.825606 140223 model_repository_manager.cc:1004] Poll failed for model directory 'encoder': unexpected directory name 'encoder' for model 'encoder_zh', directory name must equal model name
E0611 10:27:04.825819 140223 model_repository_manager.cc:1004] Poll failed for model directory 'punc': unexpected directory name 'punc' for model 'punc_zh', directory name must equal model name
E0611 10:27:04.825986 140223 model_repository_manager.cc:1004] Poll failed for model directory 'scoring': unexpected directory name 'scoring' for model 'scoring_zh', directory name must equal model name
I0611 10:27:04.826078 140223 model_lifecycle.cc:459] loading: audio_file_trans:1
I0611 10:27:04.826134 140223 model_lifecycle.cc:459] loading: audio_trans:1
I0611 10:27:04.826185 140223 model_lifecycle.cc:459] loading: feature_extractor:1
free(): invalid pointer
free(): invalid pointer
free(): invalid pointer
I0611 10:27:15.202605 140223 python_be.cc:1858] TRITONBACKEND_ModelInstanceInitialize: audio_file_trans_0 (CPU device 0)
I0611 10:27:17.205877 140223 python_be.cc:1858] TRITONBACKEND_ModelInstanceInitialize: audio_trans_0 (CPU device 0)
I0611 10:27:17.206224 140223 model_lifecycle.cc:694] successfully loaded 'audio_file_trans' version 1
I0611 10:27:19.195100 140223 python_be.cc:1858] TRITONBACKEND_ModelInstanceInitialize: feature_extractor_0 (GPU device 0)
I0611 10:27:19.195391 140223 model_lifecycle.cc:694] successfully loaded 'audio_trans' version 1
I0611 10:27:21.073233 140223 model_lifecycle.cc:694] successfully loaded 'feature_extractor' version 1
I0611 10:27:21.073575 140223 server.cc:563] 
+------------------+------+
| Repository Agent | Path |
+------------------+------+
+------------------+------+

I0611 10:27:21.073858 140223 server.cc:590] 
+---------+-------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Backend | Path                                                  | Config                                                                                                                                                        |
+---------+-------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------+
| python  | /opt/tritonserver/backends/python/libtriton_python.so | {"cmdline":{"auto-complete-config":"true","min-compute-capability":"6.000000","backend-directory":"/opt/tritonserver/backends","default-max-batch-size":"4"}} |
+---------+-------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------+

I0611 10:27:21.074072 140223 server.cc:633] 
+-------------------+---------+--------+
| Model             | Version | Status |
+-------------------+---------+--------+
| audio_file_trans  | 1       | READY  |
| audio_trans       | 1       | READY  |
| feature_extractor | 1       | READY  |
+-------------------+---------+--------+

I0611 10:27:21.207274 140223 metrics.cc:864] Collecting metrics for GPU 0: NVIDIA RTX A6000
I0611 10:27:21.207672 140223 metrics.cc:757] Collecting CPU metrics
I0611 10:27:21.207911 140223 tritonserver.cc:2264] 
+----------------------------------+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Option                           | Value                                                                                                                                                                                                |
+----------------------------------+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| server_id                        | triton                                                                                                                                                                                               |
| server_version                   | 2.30.0                                                                                                                                                                                               |
| server_extensions                | classification sequence model_repository model_repository(unload_dependents) schedule_policy model_configuration system_shared_memory cuda_shared_memory binary_tensor_data statistics trace logging |
| model_repository_path[0]         | /ws/model/zh                                                                                                                                                                                         |
| model_control_mode               | MODE_NONE                                                                                                                                                                                            |
| strict_model_config              | 0                                                                                                                                                                                                    |
| rate_limit                       | OFF                                                                                                                                                                                                  |
| pinned_memory_pool_byte_size     | 1024000000                                                                                                                                                                                           |
| cuda_memory_pool_byte_size{0}    | 1024000000                                                                                                                                                                                           |
| response_cache_byte_size         | 0                                                                                                                                                                                                    |
| min_supported_compute_capability | 6.0                                                                                                                                                                                                  |
| strict_readiness                 | 1                                                                                                                                                                                                    |
| exit_timeout                     | 30                                                                                                                                                                                                   |
+----------------------------------+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+

I0611 10:27:21.207946 140223 server.cc:264] Waiting for in-flight requests to complete.
I0611 10:27:21.207994 140223 server.cc:280] Timeout 30: Found 0 model versions that have in-flight inferences
I0611 10:27:21.208161 140223 server.cc:295] All models are stopped, unloading models
I0611 10:27:21.208191 140223 server.cc:302] Timeout 30: Found 3 live models and 0 in-flight non-inference requests
I0611 10:27:22.208314 140223 server.cc:302] Timeout 29: Found 3 live models and 0 in-flight non-inference requests
free(): invalid pointer
free(): invalid pointer
free(): invalid pointer
I0611 10:27:22.680740 140223 model_lifecycle.cc:579] successfully unloaded 'audio_file_trans' version 1
I0611 10:27:22.782311 140223 model_lifecycle.cc:579] successfully unloaded 'feature_extractor' version 1
I0611 10:27:22.899932 140223 model_lifecycle.cc:579] successfully unloaded 'audio_trans' version 1
I0611 10:27:23.208558 140223 server.cc:302] Timeout 28: Found 0 live models and 0 in-flight non-inference requests
error: creating server: Internal - failed to load all models
