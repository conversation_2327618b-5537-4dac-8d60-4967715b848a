import triton_python_backend_utils as pb_utils
import numpy as np
import multiprocessing
from torch.utils.dlpack import from_dlpack
from swig_decoders import ctc_beam_search_decoder_batch, \
    Scorer, HotWordsScorer, PathTrie, TrieVector, map_batch
import json
import os
import yaml
#from text_utils import MultiLangITN
import logging

class TritonPythonModel:
    """Your Python model must use the same class name. Every Python model
    that is created must have "TritonPythonModel" as the class name.
    """

    def initialize(self, args):
        """`initialize` is called only once when the model is being loaded.
        Implementing `initialize` function is optional. This function allows
        the model to initialize any state associated with this model.

        Parameters
        ----------
        args : dict
          Both keys and values are strings. The dictionary keys and values are:
          * model_config: A JSON string containing the model configuration
          * model_instance_kind: A string containing model instance kind
          * model_instance_device_id: A string containing model instance device ID
          * model_repository: Model repository path
          * model_version: Model version
          * model_name: Model name
        """
        # hd = logging.FileHandler(filename='log.txt', mode='w', encoding='utf-8')
        # logging.basicConfig(format='%(levelname)s - %(asctime)s - %(name)s - %(message)s',
		# 			datefmt='%y/%m/%d %H:%M:%S',
        #             handlers=[hd],
		# 			level=logging.INFO)
        # self.logger = logging.getLogger(__name__)
        # self.logger.info("log.txt 覆盖写")

        self.model_config = model_config = json.loads(args['model_config'])
        self.max_batch_size = max(model_config["max_batch_size"], 1)
        
        # Get OUTPUT0 configuration
        output0_config = pb_utils.get_output_config_by_name(
            model_config, "OUTPUT0")
        # Convert Triton types to numpy types
        self.out0_dtype = pb_utils.triton_string_to_numpy(
            output0_config['data_type'])

        # Get INPUT configuration
        batch_log_probs = pb_utils.get_input_config_by_name(
            model_config, "batch_log_probs")
        self.beam_size = batch_log_probs['dims'][-1]

        encoder_config = pb_utils.get_input_config_by_name(
            model_config, "encoder_out")
        self.data_type = pb_utils.triton_string_to_numpy(
            encoder_config['data_type'])

        self.feature_size = encoder_config['dims'][-1]

        self.lm = None
        self.hotwords_scorer = None
        self.init_ctc_rescore(self.model_config['parameters'])
        self.init_post_config(self.model_config['parameters'])
        print('Initialized Rescoring!')

    def init_ctc_rescore(self, parameters):
        num_processes = multiprocessing.cpu_count()
        cutoff_prob = 0.9999
        blank_id = 0
        alpha = 2.0
        beta = 1.0
        bidecoder = 0
        lm_path, vocab_path = None, None
        for li in parameters.items():
            key, value = li
            value = value["string_value"]
            if key == "num_processes":
                num_processes = int(value)
            elif key == "blank_id":
                blank_id = int(value)
            elif key == "cutoff_prob":
                cutoff_prob = float(value)
            elif key == "lm_path":
                lm_path = value
            elif key == "hotwords_path":
                hotwords_path = value
                # self.logger.info("111hotwords_path")
                # self.logger.info(value)
            elif key == "alpha":
                alpha = float(value)
            elif key == "beta":
                beta = float(value)
            elif key == "vocabulary":
                vocab_path = value
            elif key == "bidecoder":
                bidecoder = int(value)
            elif key == "language":
                self.lang = value

        self.num_processes = num_processes
        self.cutoff_prob = cutoff_prob
        self.blank_id = blank_id
        _, vocab = self.load_vocab(vocab_path)
        if lm_path and os.path.exists(lm_path):
            self.lm = Scorer(alpha, beta, lm_path, vocab)
            print("Successfully load language model!")
        if hotwords_path and os.path.exists(hotwords_path):
            # self.logger.info("222hotwords_path")
            # self.logger.info(hotwords_path)
            self.hotwords = self.load_hotwords(hotwords_path)
            max_order = 4
            if self.hotwords is not None:
                for w in self.hotwords:
                    max_order = max(max_order, len(w))
                self.hotwords_scorer = HotWordsScorer(self.hotwords, vocab,
                                                      window_length=max_order,
                                                      SPACE_ID=-2,
                                                      is_character_based=True)
                print(f"Successfully load hotwords! Hotwords orders = {max_order}")
        self.vocabulary = vocab
        self.bidecoder = bidecoder
        sos = eos = len(vocab) - 1
        self.sos = sos
        self.eos = eos

    def init_post_config(self, parameters):
        token_list_path, tags_list_path = None, None
        for li in parameters.items():
            key, value = li
            value = value["string_value"]
            if key == "post_enable":
                self.post_enable = int(value)
            elif key == "itn_enable":
                self.itn_enable = int(value)
            elif key == "itn_tag_fst":   # liuhuan
                self.itn_tag_fst = value
            elif key == "itn_verb_fst":   # liuhuan
                self.itn_verb_fst = value
            elif key == "itn_cache_dir":
                self.itn_cache_dir = value
            elif key == "punc_enable":
                self.punc_enable = int(value)
            elif key == "punc_token_vocab":
                token_list_path = value
            elif key == "punc_tag_vocab":
                tags_list_path = value

        if self.punc_enable:
            assert( token_list_path is not None and tags_list_path is not None )
            _, punc_vocab = self.load_vocab(token_list_path)
            for d, p in zip(self.vocabulary, punc_vocab[:len(self.vocabulary)]):
                if d != p:
                    raise pb_utils.TritonModelException("vocab mismatch between e2e_asr model and punc model.")

            punc_tag = self.load_tags_map(tags_list_path)

            vocab_idx = len(self.vocabulary)
            punc_idx_2_vocab_idx = [0] * len(punc_tag)
            for i, t in enumerate(punc_tag, start = 0):
                self.vocabulary.append(t)
                punc_idx_2_vocab_idx[i] = vocab_idx + i

            self.punc_idx_2_vocab_idx = punc_idx_2_vocab_idx
            self.punc_empty_idx = 0 # 无标点的标签IDX

        #if self.itn_enable:
        #    self.itn_engine = MultiLangITN([ self.lang ], [ self.itn_cache_dir ])   # liuhuan
        if self.itn_enable:
            if self.lang == 'zh':
                import itn_zh
                self.itn_engine = itn_zh.Itn(self.itn_tag_fst, self.itn_verb_fst)
            elif self.lang == 'en':
                import itn_en
                self.itn_engine = itn_en.Itn(self.itn_tag_fst, self.itn_verb_fst)
        # self.logger.info("**self.itn_enable**")
        # self.logger.info(self.itn_enable)

    def load_tags_map(self, punc_tag_file):
        punc2id = {}
        if punc_tag_file.endswith(".json"):
            with open(punc_tag_file, 'r', encoding='utf-8') as f:
                punc2id = json.load(f)
            id2punc = [0] * len(punc2id)
            for punc, id in punc2id.items():
                id2punc[id] = punc
        else:
            _, id2punc = self.load_vocab(punc_tag_file)

        return id2punc

    def load_vocab(self, vocab_file):
        """
        load lang_char.txt
        """
        id2vocab = {}
        with open(vocab_file, "r", encoding="utf-8") as f:
            for line in f:
                arr = line.strip().split()
                if len(arr) == 2:
                    char, id = arr
                    id2vocab[int(id)] = char
                else:
                    id2vocab[len(id2vocab.keys())] = arr[0]
        vocab = [0] * len(id2vocab)
        for id, char in id2vocab.items():
            vocab[id] = char
        return id2vocab, vocab

    def load_hotwords(self, hotwords_file):
        """
        load hotwords.yaml
        """
        # self.logger.info("333hotwords_file")
        # self.logger.info(hotwords_file)
        with open(hotwords_file, 'r', encoding="utf-8") as fin:
            configs = yaml.load(fin, Loader=yaml.FullLoader)
        return configs

    def execute(self, requests):
        """`execute` must be implemented in every Python model. `execute`
        function receives a list of pb_utils.InferenceRequest as the only
        argument. This function is called when an inference is requested
        for this model.

        Parameters
        ----------
        requests : list
          A list of pb_utils.InferenceRequest

        Returns
        -------
        list
          A list of pb_utils.InferenceResponse. The length of this list must
          be the same as `requests`
        """

        responses = []
        # Every Python backend must iterate through list of requests and create
        # an instance of pb_utils.InferenceResponse class for each of them. You
        # should avoid storing any of the input Tensors in the class attributes
        # as they will be overridden in subsequent inference requests. You can
        # make a copy of the underlying NumPy array and store it if it is
        # required.

        batch_encoder_out, batch_encoder_lens = [], []
        batch_log_probs, batch_log_probs_idx = [], []
        batch_count = []
        batch_root = TrieVector()
        batch_start = []
        root_dict = {}

        encoder_max_len = 0
        hyps_max_len = 0
        total = 0
        for request in requests:
            # Perform inference on the request and append it to responses list...
            in_0 = pb_utils.get_input_tensor_by_name(request, "encoder_out")
            in_1 = pb_utils.get_input_tensor_by_name(request, "encoder_out_lens")
            in_2 = pb_utils.get_input_tensor_by_name(request, "batch_log_probs")
            in_3 = pb_utils.get_input_tensor_by_name(request, "batch_log_probs_idx")

            batch_encoder_out.append(in_0.as_numpy())
            encoder_max_len = max(encoder_max_len, batch_encoder_out[-1].shape[1])

            cur_b_lens = in_1.as_numpy()
            batch_encoder_lens.append(cur_b_lens)
            cur_batch = cur_b_lens.shape[0]
            batch_count.append(cur_batch)

            cur_b_log_probs = in_2.as_numpy()
            cur_b_log_probs_idx = in_3.as_numpy()
            for i in range(cur_batch):
                cur_len = cur_b_lens[i]
                cur_probs = cur_b_log_probs[i][0:cur_len, :].tolist()  # T X Beam
                cur_idx = cur_b_log_probs_idx[i][0:cur_len, :].tolist()  # T x Beam
                batch_log_probs.append(cur_probs)
                batch_log_probs_idx.append(cur_idx)
                root_dict[total] = PathTrie()
                batch_root.append(root_dict[total])
                batch_start.append(True)
                total += 1
        score_hyps = ctc_beam_search_decoder_batch(batch_log_probs,
                                                   batch_log_probs_idx,
                                                   batch_root,
                                                   batch_start,
                                                   self.beam_size,
                                                   min(total, self.num_processes),
                                                   blank_id=self.blank_id,
                                                   space_id=-2,
                                                   cutoff_prob=self.cutoff_prob,
                                                   ext_scorer=self.lm,
                                                   hotwords_scorer=self.hotwords_scorer)
        all_hyps = []
        all_ctc_score = []
        max_seq_len = 0
        for seq_cand in score_hyps:
            # if candidates less than beam size
            if len(seq_cand) != self.beam_size:
                seq_cand = list(seq_cand)
                seq_cand += (self.beam_size - len(seq_cand)) * [(-float("INF"), (0,))]
            for score, hyps in seq_cand:
                all_hyps.append(list(hyps))
                all_ctc_score.append(score)
                max_seq_len = max(len(hyps), max_seq_len)
        beam_size = self.beam_size
        feature_size = self.feature_size
        hyps_max_len = max_seq_len + 2
        in_ctc_score = np.zeros((total, beam_size), dtype=self.data_type)
        in_hyps_pad_sos_eos = np.ones(
            (total, beam_size, hyps_max_len), dtype=np.int64) * self.eos
        if self.bidecoder:
            in_r_hyps_pad_sos_eos = np.ones(
                (total, beam_size, hyps_max_len), dtype=np.int64) * self.eos

        in_hyps_lens_sos = np.ones((total, beam_size), dtype=np.int32)

        in_encoder_out = np.zeros((total, encoder_max_len, feature_size),
                                  dtype=self.data_type)
        in_encoder_out_lens = np.zeros(total, dtype=np.int32)
        st = 0
        for b in batch_count:
            t = batch_encoder_out.pop(0)
            in_encoder_out[st:st + b, 0:t.shape[1]] = t
            in_encoder_out_lens[st:st + b] = batch_encoder_lens.pop(0)
            for i in range(b):
                for j in range(beam_size):
                    cur_hyp = all_hyps.pop(0)
                    cur_len = len(cur_hyp) + 2
                    in_hyp = [self.sos] + cur_hyp + [self.eos]
                    in_hyps_pad_sos_eos[st + i][j][0:cur_len] = in_hyp
                    in_hyps_lens_sos[st + i][j] = cur_len - 1
                    if self.bidecoder:
                        r_in_hyp = [self.sos] + cur_hyp[::-1] + [self.eos]
                        in_r_hyps_pad_sos_eos[st + i][j][0:cur_len] = r_in_hyp
                    in_ctc_score[st + i][j] = all_ctc_score.pop(0)
            st += b
        in_encoder_out_lens = np.expand_dims(in_encoder_out_lens, axis=1)
        in_tensor_0 = pb_utils.Tensor("encoder_out", in_encoder_out)
        in_tensor_1 = pb_utils.Tensor("encoder_out_lens", in_encoder_out_lens)
        in_tensor_2 = pb_utils.Tensor("hyps_pad_sos_eos", in_hyps_pad_sos_eos)
        in_tensor_3 = pb_utils.Tensor("hyps_lens_sos", in_hyps_lens_sos)
        input_tensors = [in_tensor_0, in_tensor_1, in_tensor_2, in_tensor_3]
        if self.bidecoder:
            in_tensor_4 = pb_utils.Tensor("r_hyps_pad_sos_eos", in_r_hyps_pad_sos_eos)
            input_tensors.append(in_tensor_4)
        in_tensor_5 = pb_utils.Tensor("ctc_score", in_ctc_score)
        input_tensors.append(in_tensor_5)
        inference_request = pb_utils.InferenceRequest(
            model_name=f'decoder_{self.lang}',
            requested_output_names=['best_index'],
            inputs=input_tensors)

        inference_response = inference_request.exec()
        if inference_response.has_error():
            raise pb_utils.TritonModelException(inference_response.error().message())
        else:
            # Extract the output tensors from the inference response.
            best_index = pb_utils.get_output_tensor_by_name(inference_response,
                                                            'best_index')
            if best_index.is_cpu():
                best_index = best_index.as_numpy()
            else:
                best_index = from_dlpack(best_index.to_dlpack())
                best_index = best_index.cpu().numpy()
            hyps = []
            idx = 0
            for cands, cand_lens in zip(in_hyps_pad_sos_eos, in_hyps_lens_sos):
                best_idx = best_index[idx][0]
                best_cand_len = cand_lens[best_idx] - 1  # remove sos
                best_cand = cands[best_idx][1: 1 + best_cand_len].tolist()
                hyps.append(best_cand)
                idx += 1

            # 标点预测
            ## 要求模型voc和标点voc一致
            if self.post_enable and self.punc_enable:
                hyps = self.batch_add_punc(hyps)

            hyps = map_batch(hyps, self.vocabulary,
                             min(multiprocessing.cpu_count(), len(in_ctc_score)))

            # 间隔符▁转换成空格
            if self.lang in ['ar', 'id']:
                hyps = [hyp.replace("▁", " ").strip() for hyp in hyps]

            # ITN功能
            if self.post_enable and self.itn_enable:
               hyps = self.batch_itn(hyps)

            # 转成json字符串
            hyps = [ {'result': h } for h in hyps ]
            hyps = [ json.dumps(h, ensure_ascii=False) for h in hyps ]

            st = 0
            for b in batch_count:
                sents = np.array(hyps[st:st + b])
                out0 = pb_utils.Tensor("OUTPUT0", sents.astype(self.out0_dtype))
                inference_response = pb_utils.InferenceResponse(output_tensors=[out0])
                responses.append(inference_response)
                st += b
        return responses

    def batch_itn(self, hyps):
        """
        批量处理itn
        """
        hyps_itn = []
        #import pdb
        #pdb.set_trace()
        for h in hyps:
            # hyps_itn.append( self.itn_engine.normalize(self.lang, h) )   # liuhuan
            hyps_itn.append( self.itn_engine.decode(h) )
            # self.logger.info("**hyps_itn h**")
            # self.logger.info(h)
        return hyps_itn

    def batch_add_punc(self, hyps):
        """
        批量处理标点预测
        """
        # 构造标点的输入数据
        #import pdb
        #pdb.set_trace()
        punc_len_slots= [16, 32, 64, 96, 128]
        empty_indexs = []
        max_len = max([len(h) for h in hyps])
        if max_len > max(punc_len_slots):
            punc_len_slots.append(max_len)
        max_len = min( [ v for v in punc_len_slots if v >= max_len ]  )
        hyps_cnt = len(hyps)
        batch_tok_ids = np.zeros((hyps_cnt, max_len), dtype=np.int32)
        batch_masks   = np.zeros((hyps_cnt, max_len), dtype=np.int32)
        for i in range(hyps_cnt):
            if len(hyps[i]) == 0:
                empty_indexs.append(i)
                batch_tok_ids[i, 0:max_len] = np.random.randint(low = 1, high = 100, size = max_len)
                batch_masks[i, 0:max_len]   = np.ones(max_len, dtype=np.int32)
            else:
                batch_tok_ids[i, 0:len(hyps[i])] = hyps[i]
                batch_masks[i, 0:len(hyps[i])]   = np.ones(len(hyps[i]), dtype=np.int32)

        # 构造请求向量
        in_tensor_0 = pb_utils.Tensor("seq_token_ids", batch_tok_ids)
        in_tensor_1 = pb_utils.Tensor("seq_masks", batch_masks)
        input_tensors = [in_tensor_0, in_tensor_1]

        inference_request = pb_utils.InferenceRequest(
            model_name=f'punc_{self.lang}',
            requested_output_names=['seq_tag_ids'],
            inputs=input_tensors)

        inference_response = inference_request.exec()

        # 处理请求结果
        if inference_response.has_error():
            raise pb_utils.TritonModelException(inference_response.error().message())
        else:
            pass
            punc_tags = pb_utils.get_output_tensor_by_name(inference_response,
                                                            'seq_tag_ids')
            if punc_tags.is_cpu():
                punc_tags = punc_tags.as_numpy()
            else:
                punc_tags = from_dlpack(punc_tags.to_dlpack())
                punc_tags = punc_tags.cpu().numpy()
        hyps_add_punc = []
        idx = 0
        for hyp_ids, punc_ids in zip(hyps, punc_tags):
            ids_add_punc = []
            punc_ids = punc_ids[:len(hyp_ids)]
            for h, p in zip(hyp_ids, punc_ids):
                ids_add_punc.append(h)
                if p != self.punc_empty_idx:
                    ids_add_punc.append(self.punc_idx_2_vocab_idx[p])
            hyps_add_punc.append(ids_add_punc)

        if len(empty_indexs) > 0:
            for i in empty_indexs:
                hyps_add_punc[i] = []

        return hyps_add_punc

    def finalize(self):
        """`finalize` is called only once when the model is being unloaded.
        Implementing `finalize` function is optional. This function allows
        the model to perform any necessary clean ups before exit.
        """
        print('Cleaning up...')
