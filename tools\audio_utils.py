import torchaudio, torch
from subprocess import PIPE, Popen
import hashlib
import pilk
import threading, os, pilk
import time, random

tmp_audio_dir = "tmp_audios"
tid = threading.currentThread().ident
pid = os.getppid()

def save_to_local_path(wav_path, waveform, sr):
    assert sr == 16000
    torchaudio.backend.sox_io_backend.save(wav_path, waveform, sr,
                                           bits_per_sample = 16)

def load_from_local_path(wav_path, sample_rate = 16000, num_channel = 1, 
        start = None, end = None, tmp_audio_dir = tmp_audio_dir):
    postfix = os.path.splitext(wav_path)[1]
    if postfix == '.silk':
        wav_new_path = f'{tmp_audio_dir}/' + \
                hashlib.md5(f'{pid}_{tid}_{wav_path}'.encode('utf-8')).hexdigest() + '.wav'
        #pilk.silk_to_wav(silk = wav_path, wav = wav_new_path, rate = sample_rate)
        pilk.silk_to_file(wav_path, wav_new_path, 1, 360, 16000, 16, 1)
        wav_path = wav_new_path
    if start is not None:
        assert end is not None
        meta = torchaudio.backend.sox_io_backend.info(wav_path)
        sr, frame_num = meta.sample_rate, meta.num_frames
        start_frame = int(start * sr)
        end_frame = int(end * sr)
        assert start_frame < frame_num
        end_frame = min(frame_num, end_frame)
        waveform, _ = torchaudio.backend.sox_io_backend.load(
            filepath=wav_path,
            num_frames=end_frame - start_frame,
            frame_offset=start_frame)
    else:
        waveform, sr = torchaudio.load(wav_path)
        #waveform, sr = torchaudio.backend.sox_io_backend.load(wav_path)  # torchaudio v2.0 取消了backend的全局设置

    if sr != sample_rate:
        waveform = torchaudio.transforms.Resample(orig_freq=sr, new_freq=sample_rate)(waveform)
    assert num_channel == 1
    if waveform.shape[0] > 1:
        waveform = torch.mean(waveform, dim=0, keepdim=True)
    return waveform, sample_rate


def load_from_http_url(url, sample_rate = 16000, num_channel = 1,
        start = None, end = None, tmp_audio_dir = tmp_audio_dir):
    pos = url.rfind('.')
    assert pos > 0
    prefix, postfix = url[:pos], url[pos + 1:]
    if postfix == 'wav':
        cmd = f'curl -s -L {url}'
        process = Popen(cmd, shell=True, stdout=PIPE)
        file_obj = process.stdout
        waveform, sr = torchaudio.backend.sox_io_backend.load(file_obj)
        if start is not None:
            assert end is not None
            waveform = waveform[:, int(start*sr):int(end*sr)] 
        if sr != sample_rate:
            waveform = torchaudio.transforms.Resample(orig_freq=sr, new_freq=sample_rate)(waveform)
        assert num_channel == 1
        if waveform.shape[0] > 1:
            waveform = torch.mean(waveform, dim=0, keepdim=True)

    else:
        name = f'{tmp_audio_dir}/' + \
                hashlib.md5(f'{pid}_{tid}_{url}'.encode('utf-8')).hexdigest() + f'.{postfix}'
        cmd = f'wget {url} -O {name}'
        ret = os.system(cmd)
        assert ret == 0
        waveform ,sample_rate = load_from_local_path(name, sample_rate = sample_rate,
                num_channel = num_channel, start = start, end = end)

    return waveform, sample_rate

def wavform_mfcc(waveform, sample_rate, delta = 2):
    # 提取MFCC特征及其一阶和二阶差分特征
    mfcc_transform = torchaudio.transforms.MFCC(sample_rate=sample_rate,
                                                n_mfcc=13,
                                                dct_type=2,
                                                norm='ortho',
                                                melkwargs={"n_fft": 400, "hop_length": 160})
    mfcc_feat = mfcc_transform(waveform[0])
    ans = [mfcc_feat]
    # Calculate the 1st derivative
    if delta >= 1:
        mfcc_delta1 = torchaudio.functional.compute_deltas(mfcc_feat)
        ans.append(mfcc_delta1)
    if delta >= 2:
        mfcc_delta2 = torchaudio.functional.compute_deltas(mfcc_delta1)
        ans.append(mfcc_delta2)

    # Concat & transpose
    ans = torch.transpose(torch.cat(ans, dim = 0), 1, 0)

    return ans

def mfcc(wav_path, delta = 2):
    waveform, sample_rate = load_from_local_path(wav_path)
    return wavform_mfcc(waveform=waveform, sample_rate=sample_rate, delta = delta)


def main(url):
    #load_from_http_url(url)
    load_from_local_path(url)


if __name__ == "__main__":
    import fire
    fire.Fire(main)

