
name: "audio_trans"
max_batch_size: 128

parameters [
  {
    key: "vad_enable",
    value: { string_value: "1"}
  },
  {
    key: "vad_type",
    value: { string_value: "webrtcvad"}
  },
  {
    key: "vad_level",
    value: { string_value: "0"}
  },
  {
    key: "vad_frame_len",
    value: { string_value: "30"}
  },
  {
    key: "vad_window_size",
    value: { string_value: "10"}
  },
  {
    key: "vad_decision_thres",
    value: { string_value: "0.9"}
  },
  {
    key: "vad_max_speech_len",
    value: { string_value: "30"}
  },
  {
    key: "vad_min_speech_len",
    value: { string_value: "5"}
  },
  {
    key: "vad_merge_sil_len",
    value: { string_value: "2"}
  },
  {
    key: "log_verbose",
    value: { string_value: "1"}
  },
  {
    key: "tmp_audio_dir",
    value: { string_value: "tmp_audios"}
  }
]

input [
  {
    name: "WAV"
    data_type: TYPE_FP32
    dims: [ -1 ]
  },
  {
    name: "LANG"
    data_type: TYPE_STRING
    dims: [ 1 ]
  }
]

output [
  {
    name: "TRANSCRIPTS"
    data_type: TYPE_STRING
    dims: [ -1 ]
  }
]

instance_group [
    {
      count: 1
      kind:  KIND_CPU
    }
]
