敏感词接口协议
2022-06-09

[toc]

创建敏感词库
基本信息
Path：/forbidden_words

**Method：**POST

接口描述： 上传一个敏感词列表文件，创建敏感词库。

敏感词列表文件格式
UTF-8 编码的文本文件，每行一个敏感词，每行不超过 10 字符，不超过 10000 行。总共可创建 200 个敏感词库。

文件示例：

张三
李四
王五
请求参数
Body（form-data）

名称	类型	是否必需	说明	默认值
name	string	否	敏感词的名称	空
file	file	是	敏感词列表文件（UTF-8 编码的 TXT 格式）	必填
lang_type	string	是	语种	必填
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
请求示例

curl -X POST '127.0.0.1:7100/forbidden_words' \
-F 'name=政治相关' \
-F 'file=@politics.txt' \
-F 'lang_type=zh-cmn-Hans-CN'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": "FW43DA3B90" // data为创建的敏感词ID
}
敏感词信息列表
基本信息
Path：/forbidden_words

**Method：**GET

接口描述： 获取所有敏感词库的信息。

返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object[]	数据
├─ id	string	敏感词ID
├─ name	string	敏感词库名称
├─ lang_type	string	语种
├─ amount	int	敏感词数量
├─ update_time	string	最近修改时间
请求示例

curl -X GET '127.0.0.1:7100/forbidden_words'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": [
        {
            "id": "FW43DA3B90",
            "name": "政治相关",
            "lang_type": "zh-cmn-Hans-CN",
            "amount": 12,
            "update_time": "2022-01-19 13:14:15"
        }
    ]
}
删除敏感词库
基本信息
Path：/forbidden_words/{forbidden_words_id}

**Method：**DELETE

接口描述： 删除指定ID的敏感词库。

请求示例

curl -X DELETE '127.0.0.1:7100/forbidden_words/FW43DA3B90'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": null
}
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
更新敏感词库
基本信息
Path：/forbidden_words/{forbidden_words_id}

**Method：**PUT

接口描述： 修改敏感词库名称或敏感词列表。

请求参数
Body（form-data）

名称	类型	是否必需	说明	默认值
name	string	否	敏感词库的名称	无
file	file	否	敏感词列表文件	无
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	敏感词ID
请求示例

修改敏感词库名称
curl -X PUT '127.0.0.1:7100/forbidden_words/FW43DA3B90' \
-F 'name=新名称'
修改敏感词列表文件
curl -X PUT '127.0.0.1:7100/forbidden_words/FW43DA3B90' \
-F 'file=@new.txt'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": "FW43DA3B90"
}
获取敏感词信息
基本信息
Path：/forbidden_words/{forbidden_words_id}

**Method：**GET

接口描述： 获取指定敏感词库ID的信息。

返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
├─ id	string	敏感词ID
├─ name	string	敏感词名称
├─ lang_type	string	语种
├─ file	string	Base64编码的敏感词文件（UTF-8 编码的 TXT 格式）
├─ amount	int	敏感词数量
├─ update_time	string	最近修改时间
请求示例

curl -X GET '127.0.0.1:7100/forbidden_words/FW43DA3B90'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": {
            "id": "FW43DA3B90",
            "name": "人名",
            "lang_type": "zh-cmn-Hans-CN",
            "file": "<Base64编码>",
            "amount": 12,
            "update_time": "2021-11-29 13:14:15"
        }
}
更新时间：2023-06-27