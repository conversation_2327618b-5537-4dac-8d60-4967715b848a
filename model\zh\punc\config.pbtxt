
name: "punc"
backend: "onnxruntime"
default_model_filename: "punc.onnx"

max_batch_size: 64
input [
  {
    name: "seq_token_ids"
    data_type: TYPE_INT32
    dims: [-1]
  },
  {
    name: "seq_masks"
    data_type: TYPE_INT32
    dims: [-1]
  }
]

output [
  {
    name: "seq_tag_ids"
    data_type: TYPE_INT32
    dims: [-1] 
  }
]

dynamic_batching {
    preferred_batch_size: [ 16, 32 ]
  }


instance_group [
    {
      count: 1
      kind:  KIND_GPU
    }
]

