# Copyright (c) 2022, NVIDIA CORPORATION.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import triton_python_backend_utils as pb_utils
from torch.utils.dlpack import to_dlpack, from_dlpack
import torch
import json

import numpy as np
import threading, os

import asyncio

import time
tid = threading.currentThread().ident
pid = os.getppid()

from audio_utils import load_from_http_url, load_from_local_path
from vad_utils import SpeechVadFrontend
import logging
from model_repo_utils import create_logger

class TritonPythonModel:
    """Your Python model must use the same class name. Every Python model
    that is created must have "TritonPythonModel" as the class name.
    """

    def initialize(self, args):
        """`initialize` is called only once when the model is being loaded.
        Implementing `initialize` function is optional. This function allows
        the model to initialize any state associated with this model.

        Parameters
        ----------
        args : dict
          Both keys and values are strings. The dictionary keys and values are:
          * model_config: A JSON string containing the model configuration
          * model_instance_kind: A string containing model instance kind
          * model_instance_device_id: A string containing model instance
          *                           device ID
          * model_repository: Model repository path
          * model_version: Model version
          * model_name: Model name
        """
        self.model_config   = model_config = json.loads(args['model_config'])
        
        model_name = args['model_name']
        self.logger = create_logger(model_name, pid, tid)
        self.logger.info(f"Module {model_name} 【initialize】")
        self.logger.info(f"model_config: {model_config}")
        self.logger.info(f"CUDA_VISIBLE_DEVICES: {os.environ['CUDA_VISIBLE_DEVICES']}")
  
        self.max_batch_size = max(model_config["max_batch_size"], 1)
        params = self.model_config['parameters']
        self._init_vad_configs(params)

        self.tmp_audio_dir  = "./tmp_audios"
        self.log_verbose    = 0
        for li in params.items():
            key, value = li
            value = value["string_value"]
            if key == "log_verbose":
                self.log_verbose = int(value)
            elif key == "tmp_audio_dir":
                self.tmp_audio_dir = value

        if not os.path.exists(self.tmp_audio_dir):
            os.makedirs(self.tmp_audio_dir)

        out1_cfg = pb_utils.get_output_config_by_name(model_config, "TRANSCRIPTS")
        self.out_dtype1 = pb_utils.triton_string_to_numpy(out1_cfg['data_type'])

        self.req_len_slots = [8, 16, 32, 64, 128]
        assert(self.req_len_slots[-1] >= self.vad_conf['max_speech_len'])
        self.req_len_slots = [ s * 1000 for s in self.req_len_slots ]
        self.logger.info(f"self.req_len_slots: {self.req_len_slots}")


    def _init_vad_configs(self, parameters):
        self.vad_enable = 0
        self.vad_conf = {
                'vad_type': 'webrtcvad',
                'vad_level': 1,
                'frame_length': 30,
                'window_size' : 10,
                'seg_thres' : 0.9, 
                'max_speech_len' : 30,
                'min_speech_len' : 5,
                'merge_sil_thres' : 2
        }

        for li in parameters.items():
            key, value = li
            value = value["string_value"]
            if key == "vad_enable":
                self.vad_enable = int(value)
            elif key == "vad_type":
                self.vad_conf['vad_type'] = value
            elif key == "vad_level":
                self.vad_conf['vad_level'] = int(value)
            elif key == "vad_frame_len":
                self.vad_conf['frame_length'] = int(value)
            elif key == "vad_window_size":
                self.vad_conf['window_size'] = int(value)
            elif key == "vad_decision_thres":
                self.vad_conf['seg_thres'] = float(value)
            elif key == "vad_max_speech_len":
                self.vad_conf['max_speech_len'] = int(value)
            elif key == "vad_min_speech_len":
                self.vad_conf['min_speech_len'] = int(value)
            elif key == "vad_merge_sil_len":
                self.vad_conf['merge_sil_thres'] = int(value)


    async def execute(self, requests):
        """`execute` must be implemented in every Python model. `execute`
        function receives a list of pb_utils.InferenceRequest as the only
        argument. This function is called when an inference is requested
        for this model.

        Parameters
        ----------
        requests : list
          A list of pb_utils.InferenceRequest

        Returns
        -------
        list
          A list of pb_utils.InferenceResponse.
          The length of this list must be the same as `requests`
        """
        batch_count = []
        total_waves = []
        total_langs = []
        responses = []
        start_time = time.time()

        # import pdb
        # pdb.set_trace()
        self.logger.info(f"INFO: Got {len(requests)} requests")
        for request in requests:
            # the requests will all have the same shape
            # different shape request will be
            # separated by triton inference server
            input0 = pb_utils.get_input_tensor_by_name(request, "WAV")
            cur_b_wav = input0.as_numpy()
            self.logger.info(f"cur_b_wav: {cur_b_wav.shape}")
            cur_batch = cur_b_wav.shape[1]
            batch_count.append(cur_batch)
            
            input1 = pb_utils.get_input_tensor_by_name(request, "LANG")
            cur_lang = input1.as_numpy()
            self.logger.info(f"cur_lang: {cur_lang}")
            lang = bytes.decode(cur_lang[0][0])

            total_waves.append(cur_b_wav)
            total_langs.append(lang)

        self.logger.info(f"len of total_waves: {len(total_waves)}")

        # 1. load audio (input0:'WAV' requests is mono 16k waveform)
        audios, audio_lens, audio_times = [], [], []
        sr = 16000
        for waveform in total_waves:
            waveform = torch.from_numpy(waveform)
            length = waveform.shape[-1] // 16   # 毫秒
            audios.append(waveform)
            audio_lens.append(length)
            audio_times.append([0, length])
        self.logger.info(f"audio_lens: {[audio_len for audio_len in audio_lens]}")

        # 2. vad
        if self.vad_enable or max(audio_lens) > 30000:
            audio_extends, audio_extends_len, audio_extends_time = [], [], []
            audio_extends_lang, audio_seg_nums = [], []
            vad_frontend = SpeechVadFrontend(**self.vad_conf)
            for waveform, lang in zip(audios, total_langs):
                segments, segment_lens, segment_times = vad_frontend.get_all_speech_segments(waveform, sr)
                # segments: List[tensor, tensor, ...]
                # segment_lens: List[int, int, ...]   # 毫秒
                # segment_times: List[ [int, int], [int, int], ...]  # 毫秒
                audio_extends.extend(segments)
                audio_extends_len.extend(segment_lens)
                audio_extends_time.extend(segment_times)
                audio_extends_lang.extend([lang] * len(segment_lens))
                audio_seg_nums.append(len(segment_lens))
        else:
            audio_extends, audio_extends_len, audio_extends_time = audios, audio_lens, audio_times
            audio_extends_lang = total_langs
            audio_seg_nums = [1] * len(audios)
        self.logger.info(f"audio_extends_len: {audio_extends_len}")

        # 3. transcribe request: async exec
        max_len = max(audio_extends_len)
        inference_response_awaits = []
        for speech, speech_len, lang in zip(audio_extends, audio_extends_len, audio_extends_lang):
            # 3.1. padding
            # better pad waveform to nearest length here
            pad_speech = speech = speech.numpy()
            
            pad_len_list = [ v for v in self.req_len_slots if v >= speech_len ]
            if len(pad_len_list):
                pad_speech_len = min(pad_len_list)
            pad_speech = np.zeros([1, pad_speech_len * sr // 1000], dtype = np.float32)
            pad_speech[0][0: len(speech)] = speech

            speech_len = np.array([[len(speech)]], dtype=np.int32)
            # 3.2. request
            in_tensor_0 = pb_utils.Tensor("WAV", pad_speech)
            in_tensor_1 = pb_utils.Tensor("WAV_LENS", speech_len)
            input_tensors = [in_tensor_0, in_tensor_1]
            inference_request = pb_utils.InferenceRequest(
                model_name=f'attention_rescoring_{lang}',       # ensemble: encoder & decoder & scoring
                requested_output_names=['TRANSCRIPTS'],
                inputs=input_tensors)
            inference_response_awaits.append(inference_request.async_exec())

        ## 4. wait async exec done
        inference_responses = await asyncio.gather(*inference_response_awaits) 
        
        # 5. warp response and return
        # 5.1 obtain segments' transcript
        segments_transcript = []
        for infer_response in inference_responses:
            if infer_response.has_error():
                raise pb_utils.TritonModelException(infer_response.error().message())
            else:
                trans = pb_utils.get_output_tensor_by_name(infer_response, 'TRANSCRIPTS')
                if trans.is_cpu():
                    trans = trans.as_numpy().tolist()[0]
                else:
                    trans = from_dlpack(trans.to_dlpack())
                    trans = trans.cpu().numpy().tolist()[0]
                trans = str(trans, encoding = "utf-8")     # type: '{...}'
                self.logger.info(f"transcript: {trans}")
                segments_transcript.append( json.loads(trans) )

        # 5.2 warp response
        results = []
        st = 0
        info_dict = {
            'file_name': 'audio stream',
            'cost_time': f"{time.time() - start_time:.4f} s",
        }
        for seg_num, waveform_dur in zip(audio_seg_nums, audio_lens):  # for every waveform in audios
            waveform_transcript = segments_transcript[st:st+seg_num]
            waveform_timestamps = audio_extends_time[st:st+seg_num]
            
            result = self._make_trans_rslt(     # type: '{...}'
                waveform_transcript,
                waveform_timestamps, 
                waveform_dur,
                info_dict
            )

            results.append(result)
            st += seg_num

        st = 0
        for b in batch_count:       # for every request
            sents = np.array(results[st:st+b])     # type: ['{...}', '{...}', ...]
            out0 = pb_utils.Tensor('TRANSCRIPTS', sents.astype(self.out_dtype1))
            inference_response = pb_utils.InferenceResponse(output_tensors=[out0])
            responses.append(inference_response)

        return responses

    def _make_trans_rslt(self, rslts, times, dur, info_dict):
        result = {
            'file_name': info_dict['file_name'],
            'duration': f"{dur/1000.0:.4f} s",
            'cost_time': info_dict['cost_time'],
            'segments_num': len(rslts)
        }

        idx = 0
        data = []
        for i, (r, t) in enumerate(zip(rslts, times), 1):
            if len(r['result']) <= 0:
                continue
            item = {}
            idx += 1
            item['seg_idx'] = idx
            item['begin'] = t[0]
            item['end'] = t[1]
            item['transcript'] = r['result']
            data.append(item)
        result['data'] = data

        result = json.dumps(result, ensure_ascii=False)
        return result


    def finalize(self):
        """`finalize` is called only once when the model is being unloaded.
        Implementing `finalize` function is optional. This function allows
        the model to perform any necessary clean ups before exit.
        """

        self.logger.info('Cleaning up...')


m = TritonPythonModel()
