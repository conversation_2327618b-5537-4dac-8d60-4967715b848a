import webrtcvad, collections
import time,random
import torch

class Frame(object):
    """Represents a "frame" of audio data."""
    def __init__(self, bytes, timestamp, duration):
        self.bytes = bytes
        self.timestamp = timestamp
        self.duration = duration

class SpeechVadFrontend(object):
    def __init__(self,
            vad_type  = 'webrtcvad',
            vad_level = 1,
            frame_length = 30,   # milliseconds
            window_size = 10,
            seg_thres = 0.9, 
            max_speech_len = 30, # seconds
            min_speech_len = 5,  # seconds
            merge_sil_thres = 2  # seconds
            ):
        '''
        vad_type : webrtc or others (to do)
        vad_level: level of vad
        frame_length: 判断是否是语音的音频时长
        window_size: 判断是否语音起始点或结束点的buffer大小（frame的个数)
        seg_thres: 判断是否语音起始点或结束点的阈值大小（buffer中超过指定阈值比例的帧为语音或静音)
        max_speech_len: 最长语音片段的长度（一般业务层调用的时候会有这个限制，防止语音过长）
        min_speech_len: 小于该长度的音频文件，不再进行vad切分
        '''
        self.vad_type = vad_type
        self.vad_level = vad_level 

        self.frame_length = frame_length
        self.window_size = window_size
        self.seg_thres = seg_thres

        self.max_speech_len = max_speech_len * 1000
        self.min_speech_len = min_speech_len * 1000
        self.merge_sil_len  = merge_sil_thres * 1000

    def _frame_generator(self, audio, sample_rate=16000):
        frame_length = int(sample_rate * (self.frame_length / 1000.0))
        offset, timestamp = 0, 0

        while offset + frame_length < audio.shape[0]:
            cur_frame_tensor = audio[offset:offset + frame_length]
            frame_bytes = cur_frame_tensor.short().numpy().tobytes()
            yield Frame(frame_bytes, timestamp, self.frame_length)
            timestamp += self.frame_length
            offset += frame_length
    

    def _vad_collector(self, vad, frames, sample_rate):
        '''
        生成一个语音片段及其起始和结束时间戳
        '''
        triggered = False
        voiced_frames = []
        ring_buffer = collections.deque(maxlen=self.window_size)
        for frame in frames:
            is_speech = vad.is_speech(frame.bytes, sample_rate)

            if not triggered:
                ring_buffer.append((frame, is_speech))
                num_voiced = len([f for f, speech in ring_buffer if speech])
                if num_voiced > self.seg_thres * ring_buffer.maxlen:
                    triggered = True
                    for f, s in ring_buffer:
                        voiced_frames.append(f)
                    ring_buffer.clear()
            else:
                voiced_frames.append(frame)
                ring_buffer.append((frame, is_speech))
                num_unvoiced = len([f for f, speech in ring_buffer if not speech])
                if num_unvoiced > self.seg_thres * ring_buffer.maxlen:
                    triggered = False
                    yield b''.join([f.bytes for f in voiced_frames]), \
                            voiced_frames[0].timestamp, \
                            voiced_frames[-1].timestamp + voiced_frames[-1].duration
                    ring_buffer.clear()
                    voiced_frames = []

        if voiced_frames:
            yield b''.join([f.bytes for f in voiced_frames]), \
                    voiced_frames[0].timestamp, \
                    voiced_frames[-1].timestamp + voiced_frames[-1].duration

    def speech_segments_generator(self, audio, sample_rate = 16000):
        '''
        根据输入的audio数组，不断生成语音片段
        '''
        if self.vad_type == 'webrtcvad':
            vad = webrtcvad.Vad(self.vad_level)

        frames   = self._frame_generator(audio, sample_rate)
        segments = self._vad_collector(vad, frames, sample_rate)
        for _, s, e in segments:
            yield s, e

    def get_all_speech_segments(self, audio, sample_rate = 16000):
        assert isinstance(audio, torch.Tensor)
        if audio.dim() == 2:
            audio = audio[0]
        assert isinstance(audio[0].item(), float)
        raw_audio = audio
        if audio[0].item() < 1.0:
            audio = audio * (1 << 15)  # b x -1
        sr = sample_rate // 1000
        audio_length_in_ms = audio.shape[0] // sr
        if audio_length_in_ms < self.min_speech_len:
            return [raw_audio], [audio_length_in_ms], [(0, audio_length_in_ms)]
        else:
            raw_segments = []
            speech_segments_gen = self.speech_segments_generator(audio, sample_rate)
            for s, e in speech_segments_gen:
                raw_segments.append((s,e))

            new_segments = []
            for s, e in raw_segments:
                if len(new_segments) == 0:
                    # split
                    while e - s > self.max_speech_len:
                        new_segments.append([s, s + self.max_speech_len])
                        s = s + self.max_speech_len
                    if e > s:
                        new_segments.append([s,e])
                    #new_segments.append([s,e])
                    continue

                if e - new_segments[-1][0] < self.min_speech_len:
                    # merge
                    new_segments[-1][1] = e
                elif new_segments[-1][1] - new_segments[-1][0] < self.min_speech_len \
                        and e - new_segments[-1][0] < self.max_speech_len \
                        and s - new_segments[-1][1] < self.merge_sil_len :
                    # merge
                    new_segments[-1][1] = e
                elif e - s > self.max_speech_len:
                    # split
                    while e - s > self.max_speech_len:
                        new_segments.append([s, s + self.max_speech_len])
                        s = s + self.max_speech_len
                    if e > s:
                        new_segments.append([s,e])
                else:
                    new_segments.append([s,e])

            #print(raw_segments)
            #print(new_segments)
            speech_segments = []
            speech_lens = []
            for s, e in new_segments:
                speech_segments.append(raw_audio[s*sr:e*sr])
                speech_lens.append(e - s)

            return speech_segments, speech_lens, new_segments

    def first_speech_timestamp(audio, sample_rate):
        '''
        获取第一个语音起始点的位置
        '''
        if self.vad_type == 'webrtcvad':
            vad = webrtcvad.Vad(self.vad_level)
        frames   = self._frame_generator(audio, sample_rate)

        ring_buffer = collections.deque(maxlen=self.window_size)
        for frame in frames:
            is_speech = vad.is_speech(frame.bytes, sample_rate)
            ring_buffer.append((frame, is_speech))
            num_voiced = len([f for f, speech in ring_buffer if speech])
            if num_voiced > self.seg_thres * ring_buffer.maxlen:
                return ring_buffer[0][0].timestamp

        return None

def main(audio_file, vad_level = 0, output_dir = None, min_speech_len = 5, merge_thres = 2):
    from audio_utils import load_from_local_path, save_to_local_path

    vad_conf = {
            'vad_type': 'webrtcvad',
            'vad_level': vad_level,
            'frame_length': 30,
            'window_size' : 10,
            'seg_thres' : 0.9, 
            'max_speech_len' : 30,
            'min_speech_len' : min_speech_len,
            'merge_sil_thres' : merge_thres
    }

    vad_frontend = SpeechVadFrontend(**vad_conf)
    audio, sr = load_from_local_path(audio_file)
    segments, segment_lens, segment_ranges = vad_frontend.get_all_speech_segments(audio, sr)
    print(segment_lens)
    print(segment_ranges)
    for i, (s,e) in enumerate(segment_ranges):
        if output_dir is None:
            output_file = f'{audio_file}_{i}.wav'
        else:
            output_file = os.path.join(output_dir, f'{i}.wav')
        save_to_local_path(output_file, audio[:, s*sr//1000 : (e+1)*sr//1000], sr)



if __name__ == "__main__":
    import fire
    fire.Fire(main)
