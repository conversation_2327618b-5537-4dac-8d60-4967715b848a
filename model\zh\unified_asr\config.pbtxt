name: "unified_asr"
backend: "python"
max_batch_size: 128

parameters [
  {
    key: "vad_enable",
    value: { string_value: "1"}
  },
  {
    key: "vad_type",
    value: { string_value: "webrtcvad"}
  },
  {
    key: "vad_level",
    value: { string_value: "0"}
  },
  {
    key: "vad_frame_len",
    value: { string_value: "30"}
  },
  {
    key: "vad_window_size",
    value: { string_value: "10"}
  },
  {
    key: "vad_decision_thres",
    value: { string_value: "0.9"}
  },
  {
    key: "vad_max_speech_len",
    value: { string_value: "30"}
  },
  {
    key: "vad_min_speech_len",
    value: { string_value: "5"}
  },
  {
    key: "vad_merge_sil_len",
    value: { string_value: "2"}
  },
  {
    key: "log_verbose",
    value: { string_value: "1"}
  },
  {
    key: "tmp_audio_dir",
    value: { string_value: "tmp_audios"}
  },
  {
    key: "num_mel_bins",
    value: { string_value: "80"}
  },
  {
    key: "frame_shift_in_ms"
    value: { string_value: "10"}
  },
  {
    key: "frame_length_in_ms"
    value: { string_value: "25"}
  },
  {
    key: "sample_rate"
    value: { string_value: "16000"}
  },
  {
    key: "encoder_model_path"
    value: { string_value: "/ws/model_repo/encoder_zh/1/encoder.onnx"}
  },
  {
    key: "language",
    value: { string_value: "zh"}
  },
  {
    key: "vocabulary",
    value: { string_value: "/ws/res/dec/zh/units.txt"}
  },
  {
    key: "bidecoder",
    value: { string_value: "1"}
  },
  {
    key: "lm_path"
    value: { string_value: ""}
  },
  {
   key: "hotwords_path",
    value: { string_value: "/ws/model_repo/scoring_zh/hotwords.yaml"}
  },
  {
    key: "post_enable"
    value: { string_value: "1"}
  },
  {
    key: "itn_enable"
    value: { string_value: "1"}
  },
  {
    key: "itn_cache_dir"
    value: { string_value: ""}
  },
  {
    key: "itn_tag_fst"
    value: { string_value: "/ws/res/post/itn/zh/zh_itn_tagger.fst"}
  },
  {
    key: "enable_punctuation_prediction"
    value: { string_value: "1"}
  },
  {
    key: "enable_modal_particle_filter"
    value: { string_value: "1"}
  },
  {
    key: "enable_words"
    value: { string_value: "0"}
  },
  {
    key: "enable_intermediate_words"
    value: { string_value: "0"}
  },
  {
    key: "enable_confidence"
    value: { string_value: "1"}
  },
  {
    key: "enable_volume"
    value: { string_value: "1"}
  },
  {
    key: "enable_lang_label"
    value: { string_value: "0"}
  },
  {
    key: "gain"
    value: { string_value: "1"}
  },
  {
    key: "max_sentence_silence"
    value: { string_value: "450"}
  }
]

input [
  {
    name: "WAV"
    data_type: TYPE_FP32
    dims: [ -1 ]
  },
  {
    name: "LANG"
    data_type: TYPE_STRING
    dims: [ 1 ]
  }
]

output [
  {
    name: "TRANSCRIPTS"
    data_type: TYPE_STRING
    dims: [ -1 ]
  }
]

instance_group [
    {
      count: 1
      kind:  KIND_GPU
    }
]
