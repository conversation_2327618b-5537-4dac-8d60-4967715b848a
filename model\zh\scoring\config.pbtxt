
name: "scoring"
backend: "python"
max_batch_size: 64

parameters [
  {
    key: "language",
    value: { string_value: "zh"}
  },
  {
    key: "vocabulary",
    value: { string_value: "/ws/res/dec/zh/units.txt"}
  },
  {
    key: "bidecoder",
    value: { string_value: "1"}
  },
  {
    key: "lm_path"
    value: { string_value: ""}
  },
  {
   key: "hotwords_path",
   value : { string_value: "/ws/model_repo/scoring_zh/hotwords.yaml"}
  },
  {
    key: "post_enable"
    value: { string_value: "1"}
  },
  {
    key: "itn_enable"
    value: { string_value: "1"}
  },
  {
    key: "itn_cache_dir"
    value: { string_value: ""}
  },
  {
    key: "itn_tag_fst"
    value: { string_value: "/ws/res/post/itn/zh/zh_itn_tagger.fst"}
  },
  {
    key: "itn_verb_fst"
    value: { string_value: "/ws/res/post/itn/zh/zh_itn_verbalizer.fst"}
  },
  {
    key: "punc_enable"
    value: { string_value: "1"}
  },
  {
    key: "punc_token_vocab"
    value: { string_value: "/ws/res/post/punc/zh/vocab"}
  },
  {
    key: "punc_tag_vocab"
    value: { string_value: "/ws/res/post/punc/zh/tag.json"}
  }
]

input [
  {
    name: "encoder_out"
    data_type: TYPE_FP16
    dims: [-1, 512] # [-1, feature_size]
  },
  {
    name: "encoder_out_lens"
    data_type: TYPE_INT32
    dims: [1]
    reshape: { shape: [ ] }
  },
  {
    name: "batch_log_probs"
    data_type: TYPE_FP16
    dims: [-1, 10] #[-1, beam_size]
  },
  {
    name: "batch_log_probs_idx"
    data_type: TYPE_INT64
    dims: [-1, 10]
  }
]

output [
  {
    name: "OUTPUT0"
    data_type: TYPE_STRING
    dims: [1]
  }
]

dynamic_batching {
    preferred_batch_size: [ 16, 32 ]
  }

instance_group [
    {
      count: 1
      kind:  KIND_GPU
    }
]
