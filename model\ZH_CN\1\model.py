import asyncio
import json
import logging
import os
import threading
import time

import numpy as np
import torch
import triton_python_backend_utils as pb_utils
from torch.utils.dlpack import from_dlpack, to_dlpack

from tools.audio_utils import load_from_http_url, load_from_local_path
from tools.model_repo_utils import create_logger
from tools.vad_utils import SpeechVadFrontend

class TritonPythonModel:
    def initialize(self, args):
        """
        Parameters
        ----------
        args : dict
          Both keys and values are strings. The dictionary keys and values are:
          * model_config: A JSON string containing the model configuration
          * model_instance_kind: A string containing model instance kind
          * model_instance_device_id: A string containing model instance
          *                           device ID
          * model_repository: Model repository path
          * model_version: Model version
          * model_name: Model name
        """
        pass


    async def execute(self, requests):
        """
        Parameters
        ----------
        requests : list
          A list of pb_utils.InferenceRequest

        Returns
        -------
        list
          A list of pb_utils.InferenceResponse.
          The length of this list must be the same as `requests`
        """
        pass

    def finalize(self):
        """`finalize` is called only once when the model is being unloaded.
        """
        self.logger.info('Cleaning up...')


if __name__ == '__main__':
    m = TritonPythonModel()