INFO - 25/06/11 10:26:56 - audio_file_trans - Module audio_file_trans 【initialize】
INFO - 25/06/11 10:26:56 - audio_file_trans - model_config: {'name': 'audio_file_trans', 'platform': '', 'backend': 'python', 'version_policy': {'latest': {'num_versions': 1}}, 'max_batch_size': 128, 'input': [{'name': 'WAV', 'data_type': 'TYPE_STRING', 'format': 'FORMAT_NONE', 'dims': [-1], 'is_shape_tensor': False, 'allow_ragged_batch': False, 'optional': False}, {'name': 'LANG', 'data_type': 'TYPE_STRING', 'format': 'FORMAT_NONE', 'dims': [1], 'is_shape_tensor': False, 'allow_ragged_batch': False, 'optional': False}], 'output': [{'name': 'TRANSCRIPTS', 'data_type': 'TYPE_STRING', 'dims': [-1], 'label_filename': '', 'is_shape_tensor': False}], 'batch_input': [], 'batch_output': [], 'optimization': {'priority': 'PRIORITY_DEFAULT', 'input_pinned_memory': {'enable': True}, 'output_pinned_memory': {'enable': True}, 'gather_kernel_buffer_threshold': 0, 'eager_batching': False}, 'instance_group': [{'name': 'audio_file_trans_0', 'kind': 'KIND_CPU', 'count': 1, 'gpus': [], 'secondary_devices': [], 'profile': [], 'passive': False, 'host_policy': ''}], 'default_model_filename': 'model.py', 'cc_model_filenames': {}, 'metric_tags': {}, 'parameters': {'vad_window_size': {'string_value': '10'}, 'vad_enable': {'string_value': '0'}, 'vad_type': {'string_value': 'webrtcvad'}, 'vad_frame_len': {'string_value': '30'}, 'vad_merge_sil_len': {'string_value': '2'}, 'log_verbose': {'string_value': '1'}, 'tmp_audio_dir': {'string_value': 'tmp_audios'}, 'vad_decision_thres': {'string_value': '0.9'}, 'vad_max_speech_len': {'string_value': '30'}, 'vad_level': {'string_value': '0'}, 'vad_min_speech_len': {'string_value': '5'}}, 'model_warmup': []}
INFO - 25/06/11 10:26:56 - audio_file_trans - CUDA_VISIBLE_DEVICES: 0
INFO - 25/06/11 10:26:56 - audio_file_trans - self.req_len_slots: [8000, 16000, 32000, 64000, 128000]
INFO - 25/06/11 10:27:01 - audio_file_trans - Cleaning up...
