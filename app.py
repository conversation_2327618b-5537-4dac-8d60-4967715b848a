#!/usr/bin/env python3
"""
FastAPI HTTP服务接口 - ASR文件转写服务
基于竞品功能实现的语音识别HTTP API
"""

import os
import sys
import json
import time
import uuid
import asyncio
import logging
import tempfile
import aiofiles
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime

import numpy as np
import requests
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# Triton client
try:
    import tritonclient.http as httpclient
    TRITON_AVAILABLE = True
except ImportError:
    print("Warning: tritonclient not available")
    TRITON_AVAILABLE = False

# Add tools to path
sys.path.append('tools')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="ASR文件转写服务",
    description="基于Triton的语音识别HTTP API服务",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
class Config:
    TRITON_SERVER_URL = os.getenv("TRITON_SERVER_URL", "localhost:8000")
    MODEL_NAME = "unified_asr"
    UPLOAD_DIR = "uploads"
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    SUPPORTED_FORMATS = [".wav", ".mp3", ".flac", ".m4a", ".aac"]
    DEFAULT_LANGUAGE = "zh"
    
config = Config()

# Ensure upload directory exists
os.makedirs(config.UPLOAD_DIR, exist_ok=True)

# Request/Response models
class TranscriptionRequest(BaseModel):
    """转写请求模型"""
    audio_url: Optional[str] = Field(None, description="音频文件URL")
    language: str = Field("zh", description="语言代码")
    enable_words: bool = Field(False, description="是否返回词级别时间戳")
    enable_confidence: bool = Field(True, description="是否返回置信度")
    enable_volume: bool = Field(True, description="是否返回音量信息")
    hotwords: Optional[List[str]] = Field(None, description="热词列表")
    forbidden_words: Optional[List[str]] = Field(None, description="敏感词列表")
    correction_words: Optional[Dict[str, str]] = Field(None, description="强制替换词典")

class TranscriptionResponse(BaseModel):
    """转写响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态: processing/completed/failed")
    text: Optional[str] = Field(None, description="识别结果文本")
    segments: Optional[List[Dict]] = Field(None, description="分段结果")
    duration: Optional[float] = Field(None, description="音频时长(秒)")
    confidence: Optional[float] = Field(None, description="整体置信度")
    volume: Optional[int] = Field(None, description="音量(0-100)")
    language: Optional[str] = Field(None, description="检测到的语言")
    cost_time: Optional[str] = Field(None, description="处理耗时")
    error_message: Optional[str] = Field(None, description="错误信息")

class TaskStatus(BaseModel):
    """任务状态模型"""
    task_id: str
    status: str
    progress: Optional[int] = None
    result: Optional[Dict] = None
    error: Optional[str] = None
    created_at: datetime
    updated_at: datetime

# Global task storage (in production, use Redis or database)
task_storage: Dict[str, TaskStatus] = {}

class TritonClient:
    """Triton客户端封装"""
    
    def __init__(self, server_url: str, model_name: str):
        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        
    def connect(self):
        """连接到Triton服务器"""
        if not TRITON_AVAILABLE:
            raise RuntimeError("Triton client not available")
            
        try:
            self.client = httpclient.InferenceServerClient(url=self.server_url)
            if not self.client.is_model_ready(self.model_name):
                raise RuntimeError(f"Model {self.model_name} is not ready")
            logger.info(f"Connected to Triton server at {self.server_url}")
        except Exception as e:
            logger.error(f"Failed to connect to Triton server: {e}")
            raise
    
    async def transcribe(self, audio_data: np.ndarray, language: str = "zh") -> Dict:
        """执行语音识别"""
        if self.client is None:
            self.connect()
        
        try:
            # Prepare inputs
            lang_array = np.array([language], dtype=object)
            
            inputs = [
                httpclient.InferInput("WAV", audio_data.shape, "FP32"),
                httpclient.InferInput("LANG", lang_array.shape, "BYTES")
            ]
            
            inputs[0].set_data_from_numpy(audio_data)
            inputs[1].set_data_from_numpy(lang_array)
            
            outputs = [httpclient.InferRequestedOutput("TRANSCRIPTS")]
            
            # Send request
            response = self.client.infer(self.model_name, inputs, outputs=outputs)
            
            # Parse result
            result_array = response.as_numpy("TRANSCRIPTS")
            if len(result_array) > 0:
                result_json = result_array[0].decode('utf-8')
                return json.loads(result_json)
            else:
                return {"text": "", "segments": [], "duration": 0}
                
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            raise

# Global Triton client
triton_client = TritonClient(config.TRITON_SERVER_URL, config.MODEL_NAME)

async def load_audio_from_url(url: str) -> np.ndarray:
    """从URL加载音频"""
    try:
        # Download audio file
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            tmp_file.write(response.content)
            tmp_path = tmp_file.name
        
        try:
            # Load audio using tools
            from audio_utils import load_from_local_path
            waveform, sample_rate = load_from_local_path(tmp_path)
            
            # Convert to numpy array
            if hasattr(waveform, 'numpy'):
                audio_data = waveform.numpy().flatten()
            else:
                audio_data = waveform.flatten()
            
            return audio_data.astype(np.float32)
            
        finally:
            # Clean up temporary file
            os.unlink(tmp_path)
            
    except Exception as e:
        logger.error(f"Failed to load audio from URL {url}: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to load audio: {str(e)}")

async def load_audio_from_file(file_path: str) -> np.ndarray:
    """从本地文件加载音频"""
    try:
        from audio_utils import load_from_local_path
        waveform, sample_rate = load_from_local_path(file_path)
        
        # Convert to numpy array
        if hasattr(waveform, 'numpy'):
            audio_data = waveform.numpy().flatten()
        else:
            audio_data = waveform.flatten()
        
        return audio_data.astype(np.float32)
        
    except Exception as e:
        logger.error(f"Failed to load audio from file {file_path}: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to load audio: {str(e)}")

async def process_transcription_task(task_id: str, audio_data: np.ndarray, request: TranscriptionRequest):
    """处理转写任务"""
    try:
        # Update task status
        task_storage[task_id].status = "processing"
        task_storage[task_id].progress = 10
        task_storage[task_id].updated_at = datetime.now()
        
        # Perform transcription
        result = await triton_client.transcribe(audio_data, request.language)
        
        # Post-process result
        processed_result = {
            "text": result.get("text", ""),
            "segments": result.get("segments", []),
            "duration": result.get("duration", 0),
            "language": request.language,
            "cost_time": result.get("info", {}).get("cost_time", "0 s")
        }
        
        # Add optional features
        if "confidence" in result:
            processed_result["confidence"] = result["confidence"]
        if "volume" in result:
            processed_result["volume"] = result["volume"]
        
        # Update task with result
        task_storage[task_id].status = "completed"
        task_storage[task_id].progress = 100
        task_storage[task_id].result = processed_result
        task_storage[task_id].updated_at = datetime.now()
        
        logger.info(f"Task {task_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Task {task_id} failed: {e}")
        task_storage[task_id].status = "failed"
        task_storage[task_id].error = str(e)
        task_storage[task_id].updated_at = datetime.now()

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("Starting ASR HTTP Service...")
    try:
        triton_client.connect()
        logger.info("ASR HTTP Service started successfully")
    except Exception as e:
        logger.error(f"Failed to start service: {e}")

@app.get("/")
async def root():
    """根路径"""
    return {"message": "ASR文件转写服务", "version": "1.0.0", "status": "running"}

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        if triton_client.client and triton_client.client.is_model_ready(config.MODEL_NAME):
            return {"status": "healthy", "triton": "connected", "model": "ready"}
        else:
            return {"status": "unhealthy", "triton": "disconnected", "model": "not_ready"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}

@app.post("/transcribe/file", response_model=TranscriptionResponse)
async def transcribe_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    language: str = Form("zh"),
    enable_words: bool = Form(False),
    enable_confidence: bool = Form(True),
    enable_volume: bool = Form(True),
    hotwords: Optional[str] = Form(None),
    forbidden_words: Optional[str] = Form(None),
    correction_words: Optional[str] = Form(None)
):
    """文件上传转写接口"""

    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in config.SUPPORTED_FORMATS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported: {config.SUPPORTED_FORMATS}"
        )

    # Check file size
    file_size = 0
    content = await file.read()
    file_size = len(content)

    if file_size > config.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File too large. Max size: {config.MAX_FILE_SIZE // 1024 // 1024}MB"
        )

    # Generate task ID
    task_id = str(uuid.uuid4())

    # Save uploaded file
    file_path = os.path.join(config.UPLOAD_DIR, f"{task_id}_{file.filename}")
    async with aiofiles.open(file_path, 'wb') as f:
        await f.write(content)

    try:
        # Load audio
        audio_data = await load_audio_from_file(file_path)

        # Parse optional parameters
        request_data = {
            "language": language,
            "enable_words": enable_words,
            "enable_confidence": enable_confidence,
            "enable_volume": enable_volume
        }

        if hotwords:
            request_data["hotwords"] = json.loads(hotwords) if isinstance(hotwords, str) else hotwords
        if forbidden_words:
            request_data["forbidden_words"] = json.loads(forbidden_words) if isinstance(forbidden_words, str) else forbidden_words
        if correction_words:
            request_data["correction_words"] = json.loads(correction_words) if isinstance(correction_words, str) else correction_words

        request = TranscriptionRequest(**request_data)

        # Create task
        task_storage[task_id] = TaskStatus(
            task_id=task_id,
            status="created",
            progress=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        # Start background processing
        background_tasks.add_task(process_transcription_task, task_id, audio_data, request)

        return TranscriptionResponse(
            task_id=task_id,
            status="processing"
        )

    except Exception as e:
        # Clean up file
        if os.path.exists(file_path):
            os.unlink(file_path)
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # Clean up uploaded file
        if os.path.exists(file_path):
            os.unlink(file_path)

@app.post("/transcribe/url", response_model=TranscriptionResponse)
async def transcribe_url(
    background_tasks: BackgroundTasks,
    request: TranscriptionRequest
):
    """URL转写接口"""

    if not request.audio_url:
        raise HTTPException(status_code=400, detail="audio_url is required")

    # Generate task ID
    task_id = str(uuid.uuid4())

    try:
        # Load audio from URL
        audio_data = await load_audio_from_url(request.audio_url)

        # Create task
        task_storage[task_id] = TaskStatus(
            task_id=task_id,
            status="created",
            progress=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        # Start background processing
        background_tasks.add_task(process_transcription_task, task_id, audio_data, request)

        return TranscriptionResponse(
            task_id=task_id,
            status="processing"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/transcribe/result/{task_id}", response_model=TranscriptionResponse)
async def get_transcription_result(task_id: str):
    """获取转写结果"""

    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task = task_storage[task_id]

    response_data = {
        "task_id": task_id,
        "status": task.status
    }

    if task.result:
        response_data.update(task.result)

    if task.error:
        response_data["error_message"] = task.error

    return TranscriptionResponse(**response_data)

@app.delete("/transcribe/result/{task_id}")
async def delete_transcription_result(task_id: str):
    """删除转写结果"""

    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    del task_storage[task_id]
    return {"message": "Task deleted successfully"}

@app.get("/transcribe/tasks")
async def list_transcription_tasks():
    """列出所有转写任务"""

    tasks = []
    for task_id, task in task_storage.items():
        tasks.append({
            "task_id": task_id,
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at,
            "updated_at": task.updated_at
        })

    return {"tasks": tasks, "total": len(tasks)}

@app.post("/transcribe/sync", response_model=TranscriptionResponse)
async def transcribe_sync(
    file: UploadFile = File(...),
    language: str = Form("zh"),
    enable_confidence: bool = Form(True),
    enable_volume: bool = Form(True)
):
    """同步转写接口（立即返回结果）"""

    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in config.SUPPORTED_FORMATS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported: {config.SUPPORTED_FORMATS}"
        )

    # Check file size (smaller limit for sync processing)
    content = await file.read()
    if len(content) > 10 * 1024 * 1024:  # 10MB limit for sync
        raise HTTPException(status_code=400, detail="File too large for sync processing (max 10MB)")

    # Generate task ID
    task_id = str(uuid.uuid4())

    # Save uploaded file temporarily
    file_path = os.path.join(config.UPLOAD_DIR, f"sync_{task_id}_{file.filename}")
    async with aiofiles.open(file_path, 'wb') as f:
        await f.write(content)

    try:
        # Load and process audio
        audio_data = await load_audio_from_file(file_path)

        # Perform transcription
        result = await triton_client.transcribe(audio_data, language)

        # Format response
        response_data = {
            "task_id": task_id,
            "status": "completed",
            "text": result.get("text", ""),
            "segments": result.get("segments", []),
            "duration": result.get("duration", 0),
            "language": language,
            "cost_time": result.get("info", {}).get("cost_time", "0 s")
        }

        # Add optional features
        if enable_confidence and "confidence" in result:
            response_data["confidence"] = result["confidence"]
        if enable_volume and "volume" in result:
            response_data["volume"] = result["volume"]

        return TranscriptionResponse(**response_data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        # Clean up temporary file
        if os.path.exists(file_path):
            os.unlink(file_path)

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
