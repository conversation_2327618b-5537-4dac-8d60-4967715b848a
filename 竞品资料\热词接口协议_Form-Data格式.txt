热词接口协议（Form-Data格式）
2023-12-18

[toc]

创建热词词库
基本信息
Path： /hotwords

Method： POST

接口描述： 上传一个热词列表文件，创建热词词库，总共可创建 200 个热词词库。

热词列表文件格式
UTF-8 编码的文本文件。

手动提取热词
每行一个热词，每行不超过 10 字符，不超过 10000 行。

文件内容示例：

张三
李四
王五
自动提取热词
不超过 3 MB。需设置参数 auto_extraction = true 。

文件内容示例：

中国第40次南极考察内陆队北京时间16日在中山站举行出征仪式，29名队员将分别前往泰山站、昆仑站和格罗夫山地区，开展相关科学考察。中国第40次南极考察由自然资源部组织。这是中国首次派出3艘船执行南极考察任务，即雪龙号、雪龙二号和天惠轮。其中，天惠轮目前正在罗斯海新站卸运建站物资，雪龙二号正在前往新西兰利特尔顿港。本次考察的一个重要方面是开展国际南极科学前沿领域合作研究，并与多国开展后勤保障方面的国际合作。
请求参数
Body（form-data）

名称	类型	是否必需	说明	默认值
name	string	否	热词的名称	空
lang_type	string	是	语种	必填
sample_rate	int	是	采样率（Hz）	必填
file	file	是	热词列表文件	必填
auto_extraction	boolean	否	是否对热词列表文件中的内容自动提取关键词（需选配对应组件）	false
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
请求示例

curl -X POST '127.0.0.1:7100/hotwords' \
-F 'name=人名' \
-F 'lang_type=zh-cmn-Hans-CN' \
-F 'sample_rate=16000' \
-F 'file=@name.txt'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": "HW50D67839" // data为创建的热词ID
}
获取热词词库信息列表
基本信息
Path： /hotwords

Method： GET

接口描述： 获取所有热词词库的信息

返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object[]	数据
├─ id	string	热词ID
├─ name	string	热词名称
├─ lang_type	string	语种
├─ status	int	状态，-1:训练失败，0:训练中，1:训练完成
├─ sample_rate	int	采样率
├─ update_time	string	最近修改时间
请求示例

curl -X GET '127.0.0.1:7100/hotwords'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": [
        {
            "id": "HW50D67839",
            "name": "人名地名",
            "lang_type": "zh-cmn-Hans-CN",
            "status": 1,
            "sample_rate": 16000,
            "update_time": "2021-11-29 13:14:15"
        }
    ]
}
删除热词词库
基本信息
Path： /hotwords/{hotwords_id}

Method： DELETE

接口描述： 删除指定 ID 的热词词库。只能删除当前没有识别请求调用的词库。

请求示例

curl -X DELETE '127.0.0.1:7100/hotwords/HW50D67839'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": null
}
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
修改热词词库信息
基本信息
Path：/hotwords/{hotwords_id}

**Method：**PUT

接口描述： 修改已有的热词词库的名称或热词列表。注意：修改热词列表时，词库内所有热词将被新列表内容覆盖。

请求参数
Body（form-data）

名称	类型	是否必需	说明	默认值
name	string	否	热词词库的名称	无
file	file	否	热词列表文件	无
auto_extraction	boolean	否	是否对热词列表中的内容自动提取关键词（需选配对应组件）	false
返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	热词id
请求示例

修改词库名称
curl -X PUT '127.0.0.1:7100/hotwords/HW50D67839' \
-F 'name=新名称'
修改热词列表文件
curl -X PUT '127.0.0.1:7100/hotwords/HW50D67839' \
-F 'file=@new.txt'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": "HW50D67839"
}
获取热词词库信息
基本信息
Path： /hotwords/{hotwords_id}

Method： GET

接口描述： 获取指定 ID 的热词词库的信息

返回参数
名称	类型	说明
status	string	状态码
message	string	状态码描述
data	object	数据
├─ id	string	热词ID
├─ name	string	热词名称
├─ lang_type	string	语种
├─ status	int	状态，-1:训练失败，0:训练中，1:训练完成
├─ sample_rate	int	采样率
├─ update_time	string	最近修改时间
├─ words	list<string>	热词列表
├─ file	string	Base64编码的热词文件（UTF-8 编码的文本格式）
注意：该字段已在V2.6.0版本中弃用，请改用words字段代替
├─ amount	int	包含的词数
请求示例

curl -X GET '127.0.0.1:7100/hotwords/HW50D67839'
返回示例

{
    "status": "00000",
    "message": "success",
    "data": {
            "id": "HW50D67839",
            "name": "人名",
            "lang_type": "zh-cmn-Hans-CN",
            "status": 1,
            "sample_rate": 16000,
            "update_time": "2021-11-29 13:14:15",
            "file": "<Base64编码>",
            "words": ["张三","李四","王五"],
            "amount": 12
        }
}
更新时间：2024-11-20