#!/bin/bash
# Deployment script for Unified ASR Model

echo "Deploying Unified ASR Model..."

# Check if Triton server is running
if pgrep -x "tritonserver" > /dev/null; then
    echo "Triton server is running. Please stop it before deployment."
    exit 1
fi

# Set model repository path
MODEL_REPO=${1:-"/opt/tritonserver/models"}

echo "Using model repository: $MODEL_REPO"

# Copy unified model to repository
if [ -d "model/zh/unified_asr" ]; then
    echo "Copying unified_asr model..."
    cp -r model/zh/unified_asr $MODEL_REPO/
    echo "✓ Model copied successfully"
else
    echo "✗ Unified ASR model not found"
    exit 1
fi

# Copy tools directory
if [ -d "tools" ]; then
    echo "Copying tools directory..."
    cp -r tools $MODEL_REPO/unified_asr/
    echo "✓ Tools copied successfully"
else
    echo "⚠ Tools directory not found"
fi

echo "Deployment complete!"
echo "Start Triton server with: tritonserver --model-repository=$MODEL_REPO"
