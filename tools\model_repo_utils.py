import logging
import time
import os

LOGDIR = os.getenv("LOGDIR", "/ws/log")

def create_logger(model_name, pid, tid):
        # logging
        now_time = time.strftime("%Y-%m-%d_%H-%M-%S", time.localtime())
        # log_name = f"log.{model_name}.{pid}_{tid}.{now_time}.txt"   # 多个实例需要不同的log_file， 否则会覆盖
        log_name = f"log.{model_name}.{now_time}.txt"   # 多个实例需要不同的log_file， 否则会覆盖
        log_file = f'{LOGDIR}/{log_name}'
        hd = logging.FileHandler(filename=log_file, mode='w', encoding='utf-8')
        logging.basicConfig(format='%(levelname)s - %(asctime)s - %(name)s - %(message)s',
					datefmt='%y/%m/%d %H:%M:%S',
					handlers=[hd],
					level=logging.INFO)
        logger = logging.getLogger(model_name)
        return logger