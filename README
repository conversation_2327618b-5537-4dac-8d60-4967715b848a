# 这个容器是用来优化Triton asr服务的，在2024年7月的基础上进一步：
# - 将子模块封装（每个语种仅启动一个python服务后端）
# - FastaAPI 开发对外接口，改进请求格式，支持网络文件、支持数据流上传
# 开发好之后，根据所需要的环境重新写Dokcerfile, 减小发布版本镜像大小

# 容器信息：
docker run -it --name asr_server \
        --net host \
        -v /home/<USER>/data/liuhuan/ASRServer:/ws \
        -w /ws \
        --shm-size 20g \
        lh_wenet_server:2.0 /bin/bash

docker exec -it asr_server bash


# 环境信息：
镜像内自带的python环境，无需额外安装Miniconda，python==3.8
所有包位于 `/usr/local/lib/python3.8/dist-packages/` 下面

k2                      1.24.4.dev20240223+cuda11.8.torch2.1.2
kaldifeat               1.25.4.dev20240223+cuda11.8.torch2.1.2
torch                   2.1.2+cu118
torchaudio              2.1.2+cu118

自带：
/usr/bin/ffmpeg
/usr/lib/python3.8/site-packages/swig_decoders-1.1-py3.8-linux-x86_64.egg   # 同理 自己的代码也可以这样打包，然后只提供 egg 文件来保护源码

构建新镜像需要：
FROM nvcr.io/nvidia/tritonserver:23.01-py3
RUN apt-get update && apt-get -y install swig && apt-get -y install python3-dev && apt-get install -y cmake
然后清理apt缓存
RUN pip3 install torch torchaudio
RUN pip3 install -v kaldifeat pyyaml onnx
然后安装 webrtcvad、pilk等
确保有 /usr/lib/pythonxxx/site-packages/swig_decoders-1.1-py3.8-linux-x86_64.egg, 并PYTHONPATH添加到了 ~/.bashrc
WORKDIR /ws
COPY 目录