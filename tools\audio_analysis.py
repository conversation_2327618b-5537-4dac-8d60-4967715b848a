import audio_utils
import torchaudio
import warnings
from math import sqrt
import numpy as np

def calEuclidDist(A, B):
    """
    :param A, B: two vectors
    :return: the Euclidean distance of A and B
    """
    return sqrt(sum([(a - b) ** 2 for (a, b) in zip(A, B)]))


def dtw(M1, M2):
    """
    Compute Dynamic Time Warping(DTW) of two mfcc sequences.
    :param M1, M2: two mfcc sequences
    :return: the minimum distance and the wrap path
    """
    # length of two sequences
    M1_len = len(M1)
    M2_len = len(M2)
    cost_0 = np.zeros((M1_len + 1, M2_len + 1))
    cost_0[0, 1:] = np.inf
    cost_0[1:, 0] = np.inf
    # Initialize the array size to M1_len * M2_len
    cost = cost_0[1:, 1:]
    for i in range(M1_len):
        for j in range(M2_len):
            cost[i, j] = calEuclidDist(M1[i], M2[j])
    # dynamic programming to calculate cost matrix
    for i in range(M1_len):
        for j in range(M2_len):
            cost[i, j] += min([cost_0[i, j], \
                               cost_0[min(i + 1, M1_len - 1), j], \
                               cost_0[i, min(j + 1, M2_len - 1)]])

    # calculate the warp path
    if len(M1) == 1:
        path = np.zeros(len(M2)), range(len(M2))
    elif len(M2) == 1:
        path = range(len(M1)), np.zeros(len(M1))
    else:
        i, j = np.array(cost_0.shape) - 2
        path_1, path_2 = [i], [j]
        # path_1, path_2 with the minimum cost is what we want
        while (i > 0) or (j > 0):
            arg_min = np.argmin((cost_0[i, j], cost_0[i, j + 1], cost_0[i + 1, j]))
            if arg_min == 0:
                i -= 1
                j -= 1
            elif arg_min == 1:
                i -= 1
            else:
                j -= 1
            path_1.insert(0, i)
            path_2.insert(0, j)
        # convert to array
        path = np.array(path_1), np.array(path_2)
    # the minimum distance is the normalized distance
    return cost[-1, -1] / sum(cost.shape), path

class SpecifySoundDectector(object):
    def __init__(self, name, threshold = 40.0, template_samples = None):
        self.name = name
        self.threshold = threshold
        self.model = None
        if template_samples is not None:
            self.model_train(template_samples)

    def model_train(self, train_files: list):
        assert self.model is None
        mfcc_list = []
        for file_path in train_files:
            if file_path[-4:] == ".wav":
                with warnings.catch_warnings():
                    warnings.simplefilter('ignore')
                    wav_mfcc = audio_utils.mfcc(file_path).numpy()
                    mfcc_list.append(wav_mfcc)
        mfcc_count = np.zeros(len(mfcc_list[0]))
        mfcc_all = np.zeros(mfcc_list[0].shape)
        for i in range(len(mfcc_list)):
            # calculate the wrap path between standard and each template
            _, path = dtw(mfcc_list[0], mfcc_list[i])
            for j in range(len(path[0])):
                mfcc_count[int(path[0][j])] += 1
                mfcc_all[int(path[0][j])] += mfcc_list[i][path[1][j]]

        # Generalization by averaging the templates
        model = np.zeros(mfcc_all.shape)
        for i in range(len(mfcc_count)):
            for j in range(len(mfcc_all[i])):
                model[i][j] = mfcc_all[i][j] / mfcc_count[i]

        self.model = model

    def dtw_distance(self, waveform, sample_rate):
        mfcc_feat = audio_utils.wavform_mfcc(waveform, sample_rate).numpy()
        min_dist, _ = dtw(self.model, mfcc_feat)
        return min_dist

    def is_matcher(self, waveform, sample_rate):
        dist = self.dtw_distance(waveform, sample_rate)
        if dist < self.threshold:
            return True
        else:
            return False


class SoundAnalysis(object):
    def __init__(self):
        pass

    def set_call_sound_model(self, waveform):
        pass

    def is_call_sound(self, waveform):
        pass


def SpecifySoundDectector_testor(train_dir, test_dir):
    import os
    train_files = []
    for d in os.listdir(train_dir):
        file_path = os.path.join(train_dir, d)
        if file_path[-4:] == ".wav":
            train_files.append(file_path)
    assert len(train_files) > 2
    ssd = SpecifySoundDectector('call', template_samples = train_files)
    test_files = []
    for d in os.listdir(test_dir):
        file_path = os.path.join(test_dir, d)
        wf, sr = audio_utils.load_from_local_path(file_path)
        dist = ssd.dtw_distance(wf, sr)
        yesno = ssd.is_matcher(wf,sr)
        print(f'{file_path} {dist} {yesno}') 

if __name__ == "__main__":
    import fire
    fire.Fire(SpecifySoundDectector_testor)
